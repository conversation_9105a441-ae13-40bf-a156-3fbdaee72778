{"Variable Declaration": {"prefix": "var", "body": ["परिवर्तनीय ${1:नाम}: ${2:संख्या} = ${3:०}"], "description": "Declare a variable with type annotation"}, "Function Declaration": {"prefix": "func", "body": ["कार्य ${1:नाम}(${2:पैरामीटर}: ${3:प्रकार}): ${4:प्रकार} {", "\t${5:// कार्य का शरीर}", "\tप्रत्यावर्तनम् ${6:मान}", "}"], "description": "Declare a function with parameters and return type"}, "Class Declaration": {"prefix": "class", "body": ["वर्ग ${1:नाम} {", "\tपरिवर्तनीय ${2:गुण}: ${3:प्रकार}", "\t", "\tकार्य प्रारम्भ(${4:पैरामीटर}: ${5:प्रकार}): शून्य {", "\t\tयह.${2:गुण} = ${4:पैरामीटर}", "\t}", "}"], "description": "Declare a class with constructor"}, "If Statement": {"prefix": "if", "body": ["यदि ${1:शर्त} {", "\t${2:// कोड}", "}"], "description": "If statement"}, "If-Else Statement": {"prefix": "ifelse", "body": ["यदि ${1:शर्त} {", "\t${2:// यदि कोड}", "} अन्यथा {", "\t${3:// अन्यथा कोड}", "}"], "description": "If-else statement"}, "Switch Statement": {"prefix": "switch", "body": ["चयन ${1:मान} {", "\t${2:केस1} -> ${3:कार्य1}", "\t${4:केस2} -> ${5:कार्य2}", "\t_ -> ${6:डिफ़ॉल्ट_कार्य}", "}"], "description": "Switch/match expression"}, "Pattern Match": {"prefix": "match", "body": ["मिलान ${1:डेटा} {", "\t${2:पैटर्न1} -> ${3:कार्य1}", "\t${4:पैटर्न2} यदि ${5:गार्ड} -> ${6:कार्य2}", "\t_ -> ${7:डिफ़ॉल्ट_कार्य}", "}"], "description": "Pattern matching with guards"}, "Loop": {"prefix": "loop", "body": ["पुनरावृत्ति ${1:शर्त} {", "\t${2:// लूप कोड}", "}"], "description": "While loop"}, "For Loop": {"prefix": "for", "body": ["पुनरावर्तनम् (${1:आइटम} इन ${2:सूची}) {", "\t${3:// लूप कोड}", "}"], "description": "For-in loop"}, "Try-Catch": {"prefix": "try", "body": ["प्रयत्नः {", "\t${1:// जोखिमभरा कोड}", "} ग्रहणम् त्रुटि(${2:संदेश}) {", "\t${3:// त्रुटि प्रबंधन}", "} अन्ततः {", "\t${4:// सफाई कोड}", "}"], "description": "Try-catch-finally block"}, "Print Statement": {"prefix": "print", "body": ["लेखय(${1:\"संदेश\"})"], "description": "Print statement"}, "Import Statement": {"prefix": "import", "body": ["आयातय \"${1:मॉड्यूल_नाम}\""], "description": "Import module"}, "List Declaration": {"prefix": "list", "body": ["परिवर्तनीय ${1:सूची_नाम}: सूची<${2:प्रकार}> = [${3:तत्व1}, ${4:तत्व2}]"], "description": "Declare a typed list"}, "Test Function": {"prefix": "test", "body": ["परीक्षणः(\"${1:परीक्षण_नाम}\") {", "\t${2:// परीक्षण कोड}", "\tअपेक्षित_समान(${3:अपेक्षित}, ${4:वास्तविक}, \"${5:संदेश}\")", "}"], "description": "Test function with assertion"}, "Test Suite": {"prefix": "testsuite", "body": ["परीक्षा_समूह(\"${1:समूह_नाम}\") {", "\tपरीक्षणः(\"${2:परीक्षण_नाम}\") {", "\t\t${3:// परीक्षण कोड}", "\t}", "}"], "description": "Test suite with test function"}, "HTTP Server": {"prefix": "server", "body": ["आयातय \"जाल_संचार\"", "", "जाल_प्रारम्भ()", "सर्वर_प्रारम्भ(${1:8080})", "", "// सर्वर लूप", "पुनरावृत्ति सत्य {", "\tपरिवर्तनीय अनुरोध = डेटा_प्राप्त()", "\tएचटीटीपी_प्रतिक्रिया(200, \"text/html\", \"${2:हैलो वर्ल्ड}\")", "}"], "description": "Basic HTTP server setup"}, "Hash Function": {"prefix": "hash", "body": ["आयातय \"गुप्तलेखन\"", "", "परिवर्तनीय हैश = sha256_हैश(\"${1:डेटा}\", ${2:लंबाई})"], "description": "Hash data using SHA-256"}, "Encrypt Data": {"prefix": "encrypt", "body": ["आयातय \"गुप्तलेखन\"", "", "परिवर्तनीय कुंजी = कुंजी_उत्पन्न(${1:32})", "परिवर्तनीय एन्क्रिप्टेड = एन्क्रिप्ट_डेटा(\"${2:डेटा}\", कुंजी, ${3:लंबाई})"], "description": "Encrypt data with generated key"}, "Current Time": {"prefix": "time", "body": ["आयातय \"काल_गणना\"", "", "परिवर्तनीय समय = वर्तमान_समय()", "लेखय(\"वर्तमान समय: \" + समय)"], "description": "Get and display current time"}, "Math Operations": {"prefix": "math", "body": ["आयातय \"गणितम्\"", "", "परिवर्तनीय परिणाम = ${1:वर्गमूल}(${2:16})", "लेखय(\"परिणाम: \" + परिणाम)"], "description": "Perform mathematical operations"}}