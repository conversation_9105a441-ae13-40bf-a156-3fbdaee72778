{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "Sanskrit Programming Language", "scopeName": "source.sanskrit", "patterns": [{"include": "#comments"}, {"include": "#strings"}, {"include": "#numbers"}, {"include": "#keywords"}, {"include": "#types"}, {"include": "#functions"}, {"include": "#operators"}, {"include": "#variables"}], "repository": {"comments": {"patterns": [{"name": "comment.line.double-slash.sanskrit", "begin": "टिप्पणी", "end": "$", "captures": {"0": {"name": "punctuation.definition.comment.sanskrit"}}}, {"name": "comment.line.semicolon.sanskrit", "begin": ";", "end": "$", "captures": {"0": {"name": "punctuation.definition.comment.sanskrit"}}}]}, "strings": {"patterns": [{"name": "string.quoted.double.sanskrit", "begin": "\"", "end": "\"", "patterns": [{"name": "constant.character.escape.sanskrit", "match": "\\\\."}]}, {"name": "string.quoted.single.sanskrit", "begin": "'", "end": "'", "patterns": [{"name": "constant.character.escape.sanskrit", "match": "\\\\."}]}]}, "numbers": {"patterns": [{"name": "constant.numeric.devanagari.sanskrit", "match": "[०-९]+\\.?[०-९]*"}, {"name": "constant.numeric.decimal.sanskrit", "match": "\\b[0-9]+\\.?[0-9]*\\b"}]}, "keywords": {"patterns": [{"name": "keyword.control.sanskrit", "match": "\\b(यदि|अन्यथा|पुनरावृत्ति|पुनरावर्तनम्|स्थगय|अनुवर्तन|प्रत्यावर्तनम्)\\b"}, {"name": "keyword.control.switch.sanskrit", "match": "\\b(चयन|मिलान)\\b"}, {"name": "keyword.control.exception.sanskrit", "match": "\\b(प्रयत्नः|ग्रहणम्|त्रुटि|अन्ततः|फेंकना)\\b"}, {"name": "keyword.declaration.sanskrit", "match": "\\b(परिवर्तनीय|क्रिया|कार्य|वर्ग|वंश)\\b"}, {"name": "keyword.import.sanskrit", "match": "\\b(आयातय)\\b"}, {"name": "keyword.operator.logical.sanskrit", "match": "\\b(सत्य|मिथ्या|और|या|नहीं)\\b"}, {"name": "keyword.other.sanskrit", "match": "\\b(लेखय|पठनम्|यह|अधिकार)\\b"}]}, "types": {"patterns": [{"name": "storage.type.sanskrit", "match": "\\b(संख्या|पङ्क्तिः|सूची|बूलियनः|कार्य|शून्य|प्रकार)\\b"}, {"name": "storage.type.generic.sanskrit", "match": "\\b(सामान्य)\\b"}, {"name": "storage.type.union.sanskrit", "match": "\\|"}, {"name": "storage.type.optional.sanskrit", "match": "\\?"}]}, "functions": {"patterns": [{"name": "entity.name.function.sanskrit", "match": "\\b(कार्य|क्रिया)\\s+([\\u0900-\\u097F\\w]+)", "captures": {"1": {"name": "keyword.declaration.function.sanskrit"}, "2": {"name": "entity.name.function.sanskrit"}}}, {"name": "support.function.builtin.sanskrit", "match": "\\b(लेखय|पठनम्|योग|गुणन|वर्गमूल|ज्या|कोज्या)\\b"}]}, "operators": {"patterns": [{"name": "keyword.operator.arithmetic.sanskrit", "match": "[+\\-*/^%]"}, {"name": "keyword.operator.comparison.sanskrit", "match": "(==|!=|<=|>=|<|>|=)"}, {"name": "keyword.operator.logical.sanskrit", "match": "(&&|\\|\\||!)"}, {"name": "keyword.operator.assignment.sanskrit", "match": "="}, {"name": "keyword.operator.member.sanskrit", "match": "\\."}, {"name": "keyword.operator.arrow.sanskrit", "match": "->"}, {"name": "keyword.operator.type.sanskrit", "match": ":"}]}, "variables": {"patterns": [{"name": "variable.other.sanskrit", "match": "\\b[\\u0900-\\u097F\\w]+\\b"}]}, "punctuation": {"patterns": [{"name": "punctuation.separator.comma.sanskrit", "match": ","}, {"name": "punctuation.terminator.statement.sanskrit", "match": ";"}, {"name": "punctuation.section.block.begin.sanskrit", "match": "\\{"}, {"name": "punctuation.section.block.end.sanskrit", "match": "\\}"}, {"name": "punctuation.section.parens.begin.sanskrit", "match": "\\("}, {"name": "punctuation.section.parens.end.sanskrit", "match": "\\)"}, {"name": "punctuation.section.brackets.begin.sanskrit", "match": "\\["}, {"name": "punctuation.section.brackets.end.sanskrit", "match": "\\]"}]}}}