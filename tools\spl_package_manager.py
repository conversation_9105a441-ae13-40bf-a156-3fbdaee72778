#!/usr/bin/env python3
"""
Sanskrit Programming Language Package Manager
पैकेज प्रबंधक (Package Manager)

SPL equivalent of pip/npm for managing Sanskrit programming language packages.
Commands:
- spl install <package>     - Install a package
- spl uninstall <package>   - Uninstall a package
- spl list                  - List installed packages
- spl search <query>        - Search for packages
- spl publish               - Publish a package
- spl init                  - Initialize a new project
"""

import os
import json
import shutil
import requests
import tarfile
import argparse
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import hashlib
import tempfile

@dataclass
class PackageInfo:
    name: str
    version: str
    description: str
    author: str
    dependencies: Dict[str, str]
    main_file: str
    keywords: List[str]
    license: str = "MIT"
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = {}
        if self.keywords is None:
            self.keywords = []

@dataclass
class ProjectConfig:
    name: str
    version: str
    description: str
    author: str
    main: str
    dependencies: Dict[str, str]
    dev_dependencies: Dict[str, str]
    scripts: Dict[str, str]
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = {}
        if self.dev_dependencies is None:
            self.dev_dependencies = {}
        if self.scripts is None:
            self.scripts = {}

class SPLPackageManager:
    """Package manager for Sanskrit Programming Language"""
    
    def __init__(self):
        self.home_dir = Path.home() / '.spl'
        self.packages_dir = self.home_dir / 'packages'
        self.cache_dir = self.home_dir / 'cache'
        self.config_file = self.home_dir / 'config.json'
        
        # Default registry URL (would be actual package registry)
        self.registry_url = "https://registry.sanskrit-lang.org"
        
        # Create directories
        self.home_dir.mkdir(exist_ok=True)
        self.packages_dir.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Load configuration
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """Load package manager configuration"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            default_config = {
                "registry_url": self.registry_url,
                "cache_ttl": 3600,  # 1 hour
                "auto_update": True
            }
            self._save_config(default_config)
            return default_config
    
    def _save_config(self, config: Dict):
        """Save package manager configuration"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
    
    def init_project(self, project_name: str, project_dir: str = "."):
        """Initialize a new SPL project"""
        project_path = Path(project_dir)
        project_path.mkdir(exist_ok=True)
        
        # Create project structure
        (project_path / "src").mkdir(exist_ok=True)
        (project_path / "tests").mkdir(exist_ok=True)
        (project_path / "docs").mkdir(exist_ok=True)
        (project_path / "lib").mkdir(exist_ok=True)
        
        # Create main file
        main_file = project_path / "src" / "मुख्य.snkt"
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write('टिप्पणी मुख्य कार्यक्रम फ़ाइल\n')
            f.write('टिप्पणी Main program file\n\n')
            f.write('लेखय("नमस्ते संसार!")\n')
        
        # Create package.json equivalent (पैकेज.json)
        package_config = ProjectConfig(
            name=project_name,
            version="1.0.0",
            description=f"A Sanskrit programming project: {project_name}",
            author="",
            main="src/मुख्य.snkt",
            dependencies={},
            dev_dependencies={
                "परीक्षण_ढांचा": "^1.0.0"
            },
            scripts={
                "चलाएं": "sanskrit src/मुख्य.snkt",
                "परीक्षण": "sanskrit tests/",
                "बिल्ड": "sanskrit build src/मुख्य.snkt"
            }
        )
        
        package_file = project_path / "पैकेज.json"
        with open(package_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(package_config), f, indent=2, ensure_ascii=False)
        
        # Create README
        readme_file = project_path / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(f"# {project_name}\n\n")
            f.write("Sanskrit Programming Language project\n\n")
            f.write("## Installation\n\n")
            f.write("```bash\n")
            f.write("spl install\n")
            f.write("```\n\n")
            f.write("## Usage\n\n")
            f.write("```bash\n")
            f.write("spl run चलाएं\n")
            f.write("```\n\n")
            f.write("## Testing\n\n")
            f.write("```bash\n")
            f.write("spl run परीक्षण\n")
            f.write("```\n")
        
        # Create .gitignore
        gitignore_file = project_path / ".gitignore"
        with open(gitignore_file, 'w', encoding='utf-8') as f:
            f.write("# SPL build artifacts\n")
            f.write("*.o\n")
            f.write("*.out\n")
            f.write("build/\n")
            f.write("dist/\n")
            f.write("\n")
            f.write("# Dependencies\n")
            f.write("spl_modules/\n")
            f.write("\n")
            f.write("# IDE files\n")
            f.write(".vscode/\n")
            f.write("*.swp\n")
            f.write("*.swo\n")
        
        print(f"✓ Initialized SPL project '{project_name}' in {project_path}")
        print("✓ Created project structure")
        print("✓ Generated पैकेज.json")
        print("✓ Created sample main file")
        print("\nNext steps:")
        print(f"  cd {project_dir}")
        print("  spl install")
        print("  spl run चलाएं")
    
    def install_package(self, package_name: str, version: str = "latest"):
        """Install a package"""
        print(f"Installing {package_name}@{version}...")
        
        # Check if package exists in registry
        package_info = self._fetch_package_info(package_name, version)
        if not package_info:
            print(f"Error: Package '{package_name}' not found")
            return False
        
        # Download package
        package_path = self._download_package(package_name, version)
        if not package_path:
            print(f"Error: Failed to download package '{package_name}'")
            return False
        
        # Install dependencies first
        if package_info.dependencies:
            print("Installing dependencies...")
            for dep_name, dep_version in package_info.dependencies.items():
                if not self._is_package_installed(dep_name, dep_version):
                    self.install_package(dep_name, dep_version)
        
        # Extract and install package
        install_path = self.packages_dir / package_name / version
        install_path.mkdir(parents=True, exist_ok=True)
        
        with tarfile.open(package_path, 'r:gz') as tar:
            tar.extractall(install_path)
        
        # Update installed packages registry
        self._update_installed_registry(package_name, version, package_info)
        
        print(f"✓ Successfully installed {package_name}@{version}")
        return True
    
    def uninstall_package(self, package_name: str):
        """Uninstall a package"""
        print(f"Uninstalling {package_name}...")
        
        if not self._is_package_installed(package_name):
            print(f"Package '{package_name}' is not installed")
            return False
        
        # Remove package directory
        package_dir = self.packages_dir / package_name
        if package_dir.exists():
            shutil.rmtree(package_dir)
        
        # Update installed packages registry
        self._remove_from_installed_registry(package_name)
        
        print(f"✓ Successfully uninstalled {package_name}")
        return True
    
    def list_packages(self):
        """List installed packages"""
        installed_file = self.home_dir / 'installed.json'
        
        if not installed_file.exists():
            print("No packages installed")
            return
        
        with open(installed_file, 'r', encoding='utf-8') as f:
            installed = json.load(f)
        
        if not installed:
            print("No packages installed")
            return
        
        print("Installed packages:")
        for package_name, info in installed.items():
            print(f"  {package_name}@{info['version']} - {info.get('description', '')}")
    
    def search_packages(self, query: str):
        """Search for packages in registry"""
        print(f"Searching for '{query}'...")
        
        # This would make an API call to the package registry
        # For now, simulate with some example packages
        example_packages = [
            {"name": "गणितम्_उन्नत", "version": "2.1.0", "description": "Advanced mathematics library"},
            {"name": "जाल_सेवा", "version": "1.5.0", "description": "Web service framework"},
            {"name": "डेटाबेस_कनेक्टर", "version": "1.2.0", "description": "Database connectivity"},
            {"name": "चित्र_प्रसंस्करण", "version": "3.0.0", "description": "Image processing library"},
            {"name": "मशीन_लर्निंग", "version": "1.0.0", "description": "Machine learning toolkit"}
        ]
        
        # Filter packages based on query
        results = [pkg for pkg in example_packages if query.lower() in pkg['name'].lower() or query.lower() in pkg['description'].lower()]
        
        if not results:
            print("No packages found")
            return
        
        print(f"Found {len(results)} packages:")
        for pkg in results:
            print(f"  {pkg['name']}@{pkg['version']} - {pkg['description']}")
    
    def publish_package(self, package_dir: str = "."):
        """Publish a package to registry"""
        package_path = Path(package_dir)
        package_file = package_path / "पैकेज.json"
        
        if not package_file.exists():
            print("Error: पैकेज.json not found. Run 'spl init' first.")
            return False
        
        with open(package_file, 'r', encoding='utf-8') as f:
            package_config = json.load(f)
        
        package_name = package_config['name']
        version = package_config['version']
        
        print(f"Publishing {package_name}@{version}...")
        
        # Create package archive
        archive_path = self._create_package_archive(package_path, package_name, version)
        
        # Upload to registry (simulated)
        print("Uploading to registry...")
        # This would make an HTTP POST to the registry
        
        print(f"✓ Successfully published {package_name}@{version}")
        return True
    
    def run_script(self, script_name: str):
        """Run a script defined in पैकेज.json"""
        package_file = Path("पैकेज.json")
        
        if not package_file.exists():
            print("Error: पैकेज.json not found")
            return False
        
        with open(package_file, 'r', encoding='utf-8') as f:
            package_config = json.load(f)
        
        scripts = package_config.get('scripts', {})
        
        if script_name not in scripts:
            print(f"Error: Script '{script_name}' not found")
            print("Available scripts:")
            for name in scripts.keys():
                print(f"  {name}")
            return False
        
        command = scripts[script_name]
        print(f"Running: {command}")
        
        # Execute the command
        os.system(command)
        return True
    
    def _fetch_package_info(self, package_name: str, version: str) -> Optional[PackageInfo]:
        """Fetch package information from registry"""
        # This would make an API call to the registry
        # For now, return a mock package info
        return PackageInfo(
            name=package_name,
            version=version,
            description=f"Mock package {package_name}",
            author="Sanskrit Community",
            dependencies={},
            main_file="main.snkt",
            keywords=["sanskrit", "library"]
        )
    
    def _download_package(self, package_name: str, version: str) -> Optional[Path]:
        """Download package from registry"""
        # This would download the actual package
        # For now, create a mock package file
        cache_file = self.cache_dir / f"{package_name}-{version}.tar.gz"
        
        # Create a mock tar.gz file
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create mock package structure
            package_dir = temp_path / package_name
            package_dir.mkdir()
            
            # Create main file
            main_file = package_dir / "main.snkt"
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write(f'टिप्पणी {package_name} library\n')
                f.write('लेखय("Package loaded successfully")\n')
            
            # Create package archive
            with tarfile.open(cache_file, 'w:gz') as tar:
                tar.add(package_dir, arcname=package_name)
        
        return cache_file
    
    def _is_package_installed(self, package_name: str, version: str = None) -> bool:
        """Check if package is installed"""
        installed_file = self.home_dir / 'installed.json'
        
        if not installed_file.exists():
            return False
        
        with open(installed_file, 'r', encoding='utf-8') as f:
            installed = json.load(f)
        
        if package_name not in installed:
            return False
        
        if version and installed[package_name]['version'] != version:
            return False
        
        return True
    
    def _update_installed_registry(self, package_name: str, version: str, package_info: PackageInfo):
        """Update installed packages registry"""
        installed_file = self.home_dir / 'installed.json'
        
        if installed_file.exists():
            with open(installed_file, 'r', encoding='utf-8') as f:
                installed = json.load(f)
        else:
            installed = {}
        
        installed[package_name] = {
            'version': version,
            'description': package_info.description,
            'author': package_info.author,
            'installed_at': str(Path.cwd())
        }
        
        with open(installed_file, 'w', encoding='utf-8') as f:
            json.dump(installed, f, indent=2, ensure_ascii=False)
    
    def _remove_from_installed_registry(self, package_name: str):
        """Remove package from installed registry"""
        installed_file = self.home_dir / 'installed.json'
        
        if not installed_file.exists():
            return
        
        with open(installed_file, 'r', encoding='utf-8') as f:
            installed = json.load(f)
        
        if package_name in installed:
            del installed[package_name]
        
        with open(installed_file, 'w', encoding='utf-8') as f:
            json.dump(installed, f, indent=2, ensure_ascii=False)
    
    def _create_package_archive(self, package_path: Path, package_name: str, version: str) -> Path:
        """Create package archive for publishing"""
        archive_path = self.cache_dir / f"{package_name}-{version}.tar.gz"
        
        with tarfile.open(archive_path, 'w:gz') as tar:
            tar.add(package_path, arcname=package_name)
        
        return archive_path

def main():
    parser = argparse.ArgumentParser(description='Sanskrit Programming Language Package Manager')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Init command
    init_parser = subparsers.add_parser('init', help='Initialize a new project')
    init_parser.add_argument('name', help='Project name')
    init_parser.add_argument('--dir', default='.', help='Project directory')
    
    # Install command
    install_parser = subparsers.add_parser('install', help='Install a package')
    install_parser.add_argument('package', nargs='?', help='Package name')
    install_parser.add_argument('--version', default='latest', help='Package version')
    
    # Uninstall command
    uninstall_parser = subparsers.add_parser('uninstall', help='Uninstall a package')
    uninstall_parser.add_argument('package', help='Package name')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List installed packages')
    
    # Search command
    search_parser = subparsers.add_parser('search', help='Search for packages')
    search_parser.add_argument('query', help='Search query')
    
    # Publish command
    publish_parser = subparsers.add_parser('publish', help='Publish a package')
    publish_parser.add_argument('--dir', default='.', help='Package directory')
    
    # Run command
    run_parser = subparsers.add_parser('run', help='Run a script')
    run_parser.add_argument('script', help='Script name')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = SPLPackageManager()
    
    if args.command == 'init':
        manager.init_project(args.name, args.dir)
    elif args.command == 'install':
        if args.package:
            manager.install_package(args.package, args.version)
        else:
            # Install from पैकेज.json
            print("Installing dependencies from पैकेज.json...")
            # Implementation would read पैकेज.json and install all dependencies
    elif args.command == 'uninstall':
        manager.uninstall_package(args.package)
    elif args.command == 'list':
        manager.list_packages()
    elif args.command == 'search':
        manager.search_packages(args.query)
    elif args.command == 'publish':
        manager.publish_package(args.dir)
    elif args.command == 'run':
        manager.run_script(args.script)

if __name__ == '__main__':
    main()
