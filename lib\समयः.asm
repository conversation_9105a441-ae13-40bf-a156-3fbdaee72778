section .data
    ; Time format strings
    time_fmt db '%02d:%02d:%02d', 0
    date_fmt db '%02d-%02d-%04d', 0
    
    ; Error messages
    error_invalid_time db 'Error: Invalid time value', 0xA
    error_invalid_len equ $ - error_invalid_time

section .text
    global वर्तमान     ; Current time
    global दिनांक      ; Current date
    global स्वरूप      ; Format time
    global योग        ; Add time
    global अंतर       ; Time difference

; Get current time (वर्तमान)
; Returns:
;   eax = hours
;   ebx = minutes
;   ecx = seconds
वर्तमान:
    push edx
    
    ; Get system time
    mov eax, 13     ; sys_time
    int 0x80
    
    ; Convert to HH:MM:SS
    mov ebx, 3600
    xor edx, edx
    div ebx
    push edx        ; Save remainder
    
    ; Hours
    mov ebx, 24
    xor edx, edx
    div ebx
    mov eax, edx    ; Hours in eax
    
    ; Minutes and seconds
    pop edx
    push eax        ; Save hours
    mov eax, edx
    mov ebx, 60
    xor edx, edx
    div ebx
    mov ebx, eax    ; Minutes in ebx
    mov ecx, edx    ; Seconds in ecx
    pop eax         ; Restore hours
    
    pop edx
    ret

; Get current date (दिनांक)
; Returns:
;   eax = day
;   ebx = month
;   ecx = year
दिनांक:
    push edx
    
    ; Get system time
    mov eax, 13     ; sys_time
    int 0x80
    
    ; Convert to date
    add eax, 0x7B2000    ; Add Unix epoch offset (1970)
    
    ; Calculate year
    mov ebx, 365
    xor edx, edx
    div ebx
    mov ecx, eax    ; Year in ecx
    
    ; Calculate month and day
    mov eax, edx
    mov ebx, 30
    xor edx, edx
    div ebx
    inc eax         ; Month (1-based)
    mov ebx, eax    ; Month in ebx
    inc edx         ; Day (1-based)
    mov eax, edx    ; Day in eax
    
    pop edx
    ret

; Format time (स्वरूप)
; Parameters:
;   eax = hours
;   ebx = minutes
;   ecx = seconds
;   edi = output buffer
; Returns: none
स्वरूप:
    push eax
    push ebx
    push ecx
    push edx
    push esi
    
    mov esi, time_fmt
    
    ; Format hours
    mov edx, eax
    call format_number
    
    ; Format minutes
    mov edx, ebx
    call format_number
    
    ; Format seconds
    mov edx, ecx
    call format_number
    
    mov byte [edi], 0    ; Null terminate
    
    pop esi
    pop edx
    pop ecx
    pop ebx
    pop eax
    ret

; Helper function to format 2-digit numbers
; Parameters:
;   edx = number to format
;   edi = output buffer
format_number:
    push eax
    push ebx
    
    mov eax, edx
    mov ebx, 10
    div bl
    
    add al, '0'
    mov [edi], al
    inc edi
    add ah, '0'
    mov [edi], ah
    inc edi
    
    pop ebx
    pop eax
    ret

; Add time (योग)
; Parameters:
;   eax = hours1
;   ebx = minutes1
;   ecx = seconds1
;   edx = hours2
;   esi = minutes2
;   edi = seconds2
; Returns:
;   eax = result hours
;   ebx = result minutes
;   ecx = result seconds
योग:
    push edx
    
    ; Add seconds
    add ecx, edi
    mov eax, ecx
    mov ebx, 60
    xor edx, edx
    div ebx
    mov ecx, edx    ; Final seconds
    
    ; Add minutes
    add eax, esi
    add eax, ebx
    xor edx, edx
    div ebx
    mov ebx, edx    ; Final minutes
    
    ; Add hours
    add eax, edx
    mov ebx, 24
    xor edx, edx
    div ebx
    mov eax, edx    ; Final hours
    
    pop edx
    ret

; Calculate time difference (अंतर)
; Parameters:
;   eax = hours1
;   ebx = minutes1
;   ecx = seconds1
;   edx = hours2
;   esi = minutes2
;   edi = seconds2
; Returns:
;   eax = difference in seconds
अंतर:
    push ebx
    push ecx
    push edx
    
    ; Convert first time to seconds
    imul eax, 3600
    imul ebx, 60
    add eax, ebx
    add eax, ecx
    mov ebx, eax    ; Save first time
    
    ; Convert second time to seconds
    imul edx, 3600
    imul esi, 60
    mov eax, edx
    add eax, esi
    add eax, edi
    
    ; Calculate difference
    sub eax, ebx
    
    pop edx
    pop ecx
    pop ebx
    ret