# Sanskrit Programming Language Specification

## Introduction
The Sanskrit Programming Language (SPL) is a unique programming language that uses Sanskrit keywords and Devanagari script for its core syntax. This specification document outlines the language's grammar, syntax, and features.

## Lexical Structure

### Keywords
The following Sanskrit words are reserved as keywords:

- `लेखय` (lekh) - Print/Output command
- `परिवर्तनीय` (parivartaniya) - Variable declaration
- `क्रिया` (kriya) - Function definition
- `यदि` (yadi) - If condition
- `अन्यथा` (anyatha) - Else condition
- `पुनरावृत्ति` (punaravritti) - Loop/While construct
- `प्रत्यावर्तनम्` (pratyavartanam) - Return statement
- `सत्य` (satya) - Boolean true
- `मिथ्या` (mithya) - Boolean false
- `आयातय` (ayataya) - Import statement
- `स्थगय` (sthagay) - Break statement
- `अनुवर्तन` (anuvartan) - Continue statement
- `वर्ग` (varg) - Class definition
- `वंश` (vansh) - Inheritance

#### New Language Features
- `चयन` (chayan) - Switch/Match expression
- `मिलान` (milan) - Pattern matching
- `प्रयत्नः` (prayatnah) - Try block
- `ग्रहणम्` (grahanam) - Catch block
- `त्रुटि` (truti) - Error/Exception
- `अन्ततः` (antatah) - Finally block
- `फेंकना` (phenkana) - Throw statement

#### Type System Keywords
- `संख्या` (sankhya) - Number type
- `पङ्क्तिः` (panktih) - String type
- `सूची` (suchi) - List/Array type
- `बूलियनः` (booleanah) - Boolean type
- `कार्य` (karya) - Function type
- `शून्य` (shunya) - Null/Void type
- `प्रकार` (prakar) - Type annotation

### Literals

#### Numbers
Numbers are written using Devanagari numerals:
- `०, १, २, ३, ४, ५, ६, ७, ८, ९`

#### Strings
Strings are enclosed in double quotes: `"नमस्ते"`

### Operators

#### Arithmetic Operators
- `+` Addition
- `-` Subtraction
- `*` Multiplication
- `/` Division
- `^` Exponentiation
- `%` Modulus
- `.` Member access operator

#### Comparison Operators
- `>` Greater than
- `<` Less than
- `=` Equal to

## Syntax

### Variable Declaration
```
परिवर्तनीय नाम = १०
```

### Print Statement
```
लेखय("नमस्ते विश्व")
```

### Conditional Statement
```
यदि १० > ५ {
    लेखय("सत्य")
} अन्यथा {
    लेखय("मिथ्या")
}
```

### Loop Structure
```
पुनरावृत्ति ५ > ० {
    लेखय("पुनः")
}
```

### Function Definition
```
क्रिया योग(क: संख्या, ख: संख्या): संख्या {
    परिवर्तनीय फल: संख्या = क + ख
    लेखय(फल)
    प्रत्यावर्तनम् फल
}
```

### Switch/Match Expression
```
चयन मान {
    १ -> लेखय("एक")
    २ -> लेखय("दो")
    ३, ४, ५ -> लेखय("तीन से पांच")
    _ -> लेखय("अन्य")
}
```

### Pattern Matching
```
मिलान डेटा {
    सूची(प्रथम, ...शेष) -> लेखय("सूची: " + प्रथम)
    संख्या(x) यदि x > १० -> लेखय("बड़ी संख्या")
    पङ्क्तिः(s) -> लेखय("वाक्य: " + s)
    _ -> लेखय("अज्ञात प्रकार")
}
```

### Exception Handling
```
प्रयत्नः {
    परिवर्तनीय परिणाम = जोखिमभरा_कार्य()
    लेखय(परिणाम)
} ग्रहणम् त्रुटि(संदेश) {
    लेखय("त्रुटि हुई: " + संदेश)
} अन्ततः {
    लेखय("सफाई कार्य")
}
```

### Class Inheritance
```
वर्ग पशु {
    परिवर्तनीय नाम: पङ्क्तिः

    क्रिया आवाज(): पङ्क्तिः {
        प्रत्यावर्तनम् "सामान्य आवाज"
    }
}

वर्ग कुत्ता वंश पशु {
    क्रिया आवाज(): पङ्क्तिः {
        प्रत्यावर्तनम् "भौं भौं"
    }
}
```

## File Format
- Sanskrit programs use the `.snkt` file extension
- Files should be encoded in UTF-8 to support Devanagari characters

## Error Handling
- Syntax errors are reported with line numbers
- Runtime errors include descriptive messages in both Sanskrit and English

## Implementation Notes
- The interpreter is implemented in x86 Assembly
- System calls are used for I/O operations
- Memory management is handled manually

## Best Practices
1. Use meaningful variable names in Sanskrit
2. Include comments to explain complex logic
3. Follow consistent indentation
4. Keep functions focused and small
5. Use appropriate error handling

### Import Statement
```
आयातय "मानक"
```

## Future Considerations
- Support for additional arithmetic operators
- Enhanced string manipulation
- Standard library functions
- Object-oriented programming features