section .data
    ; Array-related keywords and messages
    kw_array db 'सूची', 0       ; Array keyword
    kw_push db 'योजय', 0        ; Push operation
    kw_pop db 'निकाल', 0         ; Pop operation
    kw_get db 'प्राप्त', 0       ; Get element
    kw_set db 'स्थापय', 0        ; Set element
    kw_len db 'मात्रा', 0        ; Length operation

    array_error_index db 'त्रुटि: Invalid array index', 0xA
    array_error_len equ $ - array_error_index
    array_error_full db 'त्रुटि: Array is full', 0xA
    array_error_full_len equ $ - array_error_full
    array_error_empty db 'त्रुटि: Array is empty', 0xA
    array_error_empty_len equ $ - array_error_empty

    ; Array configuration
    max_arrays equ 16           ; Maximum number of arrays
    array_size equ 100         ; Size of each array

section .bss
    ; Array storage
    arrays resb max_arrays * array_size * 4  ; Each element is 4 bytes
    array_lengths resb max_arrays * 4        ; Length of each array
    array_count dd 0                         ; Number of arrays

section .text
    global init_array
    global array_push
    global array_pop
    global array_get
    global array_set
    global array_length

; Initialize a new array
init_array:
    push ebp
    mov ebp, esp
    
    ; Check if we can create more arrays
    mov eax, [array_count]
    cmp eax, max_arrays
    jge array_init_error
    
    ; Initialize array length to 0
    mov dword [array_lengths + eax * 4], 0
    
    ; Increment array count and return array ID
    inc dword [array_count]
    dec eax
    
    mov esp, ebp
    pop ebp
    ret

; Push value to array
; Parameters: array_id (ebp+8), value (ebp+12)
array_push:
    push ebp
    mov ebp, esp
    
    ; Get array length
    mov eax, [ebp + 8]        ; array_id
    mov ecx, [array_lengths + eax * 4]
    
    ; Check if array is full
    cmp ecx, array_size
    jge array_full_error
    
    ; Calculate element position
    imul eax, array_size * 4
    lea edi, [arrays + eax + ecx * 4]
    
    ; Store value
    mov eax, [ebp + 12]       ; value
    mov [edi], eax
    
    ; Increment length
    mov eax, [ebp + 8]        ; array_id
    inc dword [array_lengths + eax * 4]
    
    mov esp, ebp
    pop ebp
    ret 8

; Pop value from array
; Parameter: array_id (ebp+8)
array_pop:
    push ebp
    mov ebp, esp
    
    ; Get array length
    mov eax, [ebp + 8]        ; array_id
    mov ecx, [array_lengths + eax * 4]
    
    ; Check if array is empty
    test ecx, ecx
    jz array_empty_error
    
    ; Calculate element position
    dec ecx
    imul eax, array_size * 4
    lea edi, [arrays + eax + ecx * 4]
    
    ; Get value
    mov eax, [edi]
    
    ; Decrement length
    mov edi, [ebp + 8]        ; array_id
    dec dword [array_lengths + edi * 4]
    
    mov esp, ebp
    pop ebp
    ret 4

; Get array element
; Parameters: array_id (ebp+8), index (ebp+12)
array_get:
    push ebp
    mov ebp, esp
    
    ; Check index bounds
    mov eax, [ebp + 8]        ; array_id
    mov ecx, [ebp + 12]       ; index
    cmp ecx, [array_lengths + eax * 4]
    jge array_index_error
    
    ; Calculate element position
    imul eax, array_size * 4
    lea edi, [arrays + eax + ecx * 4]
    
    ; Return value
    mov eax, [edi]
    
    mov esp, ebp
    pop ebp
    ret 8

; Set array element
; Parameters: array_id (ebp+8), index (ebp+12), value (ebp+16)
array_set:
    push ebp
    mov ebp, esp
    
    ; Check index bounds
    mov eax, [ebp + 8]        ; array_id
    mov ecx, [ebp + 12]       ; index
    cmp ecx, [array_lengths + eax * 4]
    jge array_index_error
    
    ; Calculate element position
    imul eax, array_size * 4
    lea edi, [arrays + eax + ecx * 4]
    
    ; Store value
    mov eax, [ebp + 16]       ; value
    mov [edi], eax
    
    mov esp, ebp
    pop ebp
    ret 12

; Get array length
; Parameter: array_id (ebp+8)
array_length:
    push ebp
    mov ebp, esp
    
    mov eax, [ebp + 8]        ; array_id
    mov eax, [array_lengths + eax * 4]
    
    mov esp, ebp
    pop ebp
    ret 4

; Error handlers
array_init_error:
    mov eax, -1
    mov esp, ebp
    pop ebp
    ret

array_full_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, array_error_full
    mov edx, array_error_full_len
    int 0x80
    mov eax, -1
    mov esp, ebp
    pop ebp
    ret 8

array_empty_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, array_error_empty
    mov edx, array_error_empty_len
    int 0x80
    mov eax, -1
    mov esp, ebp
    pop ebp
    ret 4

array_index_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, array_error_index
    mov edx, array_error_len
    int 0x80
    mov eax, -1
    mov esp, ebp
    pop ebp
    ret