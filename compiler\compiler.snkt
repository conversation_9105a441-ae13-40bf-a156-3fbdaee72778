टिप्पणी संस्कृत कम्पाइलर का मूल कार्यान्वयन

टिप्पणी टोकन प्रकार परिभाषाएं
परिवर्तनीय टोकन_कुंजीशब्द = १
परिवर्तनीय टोकन_पहचानकर्ता = २
परिवर्तनीय टोकन_संख्या = ३
परिवर्तनीय टोकन_वाक्य = ४
परिवर्तनीय टोकन_संचालक = ५
परिवर्तनीय टोकन_वर्ग = ६
परिवर्तनीय टोकन_वंश = ७

टिप्पणी नए संचालक
परिवर्तनीय संचालक_घात = "^"
परिवर्तनीय संचालक_मॉड = "%"
परिवर्तनीय संचालक_डॉट = "."

टिप्पणी नियंत्रण प्रवाह कुंजीशब्द
परिवर्तनीय कुंजी_स्थगय = "स्थगय"
परिवर्तनीय कुंजी_अनुवर्तन = "अनुवर्तन"

टिप्पणी टोकनाइज़र कार्यान्वयन
कार्य टोकनबनाना(स्रोत) {
    परिवर्तनीय टोकन_सूची = सूचि()
    परिवर्तनीय वर्तमान_स्थिति = ०
    
    पुनरावर्तनम् (वर्तमान_स्थिति < स्रोत.लंबाई()) {
        परिवर्तनीय वर्तमान_वर्ण = स्रोत[वर्तमान_स्थिति]
        
        यदि (वर्ण_रिक्त(वर्तमान_वर्ण)) {
            परिवर्तनीय वर्तमान_स्थिति = वर्तमान_स्थिति + १
            जारी
        }
        
        यदि (वर्ण_अंक(वर्तमान_वर्ण)) {
            परिवर्तनीय संख्या = पढ़_संख्या(स्रोत, वर्तमान_स्थिति)
            टोकन_सूची.जोड़ें({प्रकार: टोकन_संख्या, मान: संख्या})
        }
        
        यदि (वर्ण_पहचानकर्ता(वर्तमान_वर्ण)) {
            परिवर्तनीय पहचानकर्ता = पढ़_पहचानकर्ता(स्रोत, वर्तमान_स्थिति)
            टोकन_सूची.जोड़ें({प्रकार: टोकन_पहचानकर्ता, मान: पहचानकर्ता})
        }
    }
    
    टोकन_सूची
}

टिप्पणी सहायक कार्य
कार्य वर्ण_रिक्त(वर्ण) {
    यदि (वर्ण == " " || वर्ण == "\n" || वर्ण == "\t") {
        सत्य
    } अन्यथा {
        असत्य
    }
}

कार्य वर्ण_अंक(वर्ण) {
    यदि (वर्ण >= "०" && वर्ण <= "९") {
        सत्य
    } अन्यथा {
        असत्य
    }
}

कार्य वर्ण_पहचानकर्ता(वर्ण) {
    यदि ((वर्ण >= "अ" && वर्ण <= "ह") || वर्ण == "_") {
        सत्य
    } अन्यथा {
        असत्य
    }
}