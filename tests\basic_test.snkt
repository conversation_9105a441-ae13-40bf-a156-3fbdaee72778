# Basic functionality test for Sanskrit language interpreter

# Variable declaration and assignment
परिवर्तनीय संख्या = १०
परिवर्तनीय नाम = "राम"

# Arithmetic operations
परिवर्तनीय योग = संख्या + ५
परिवर्तनीय गुणन = योग * २

# Conditional statement
यदि संख्या > ५
    लेखय "संख्या ५ से बड़ी है"

# Loop example
पुनरावर्तनम् ३
    लेखय "नमस्ते"

# Array operations
सूचि फल = ["आम", "केला", "सेब"]
लेखय फल[०]

# Function definition and call
कार्य नमस्कार(नाम)
    लेखय "नमस्ते " + नाम

नमस्कार("जगत")