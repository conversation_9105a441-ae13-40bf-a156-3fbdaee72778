section .data
    ; HTTP response headers
    http_ok db 'HTTP/1.1 200 OK', 0xD, 0xA
    http_ok_len equ $ - http_ok
    content_type db 'Content-Type: text/html; charset=UTF-8', 0xD, 0xA
    content_type_len equ $ - content_type
    server_header db 'Server: संस्कृत/1.0', 0xD, 0xA
    server_header_len equ $ - server_header
    headers_end db 0xD, 0xA
    headers_end_len equ $ - headers_end

    ; Error messages
    error_bind db 'त्रुटि: Cannot bind to port', 0xA
    error_bind_len equ $ - error_bind
    error_listen db 'त्रुटि: Listen failed', 0xA
    error_listen_len equ $ - error_listen

    ; Configuration
    port dw 8080
    max_connections equ 10
    buffer_size equ 4096

section .bss
    server_socket resd 1
    client_socket resd 1
    request_buffer resb buffer_size
    response_buffer resb buffer_size
    route_table resb 1024  ; Simple routing table
    route_count resd 1

section .text
    global सेवक_आरम्भ    ; server_start
    global मार्ग_योजय     ; add_route
    global अनुरोध_संसाधय  ; handle_request

; Initialize and start the web server
सेवक_आरम्भ:
    push ebp
    mov ebp, esp

    ; Create socket
    mov eax, 359        ; sys_socket
    mov ebx, 2          ; AF_INET
    mov ecx, 1          ; SOCK_STREAM
    mov edx, 0          ; protocol
    int 0x80

    mov [server_socket], eax

    ; Bind socket
    mov eax, 361        ; sys_bind
    mov ebx, [server_socket]
    mov ecx, port
    mov edx, 2          ; AF_INET
    int 0x80

    test eax, eax
    js bind_error

    ; Listen for connections
    mov eax, 363        ; sys_listen
    mov ebx, [server_socket]
    mov ecx, max_connections
    int 0x80

    test eax, eax
    js listen_error

    mov esp, ebp
    pop ebp
    ret

; Add a route to the routing table
; Parameters: path (ebp+8), handler (ebp+12)
मार्ग_योजय:
    push ebp
    mov ebp, esp
    
    ; Add route entry to table
    mov eax, [route_count]
    imul eax, 8         ; Each route entry is 8 bytes (path + handler)
    lea edi, [route_table + eax]
    
    mov eax, [ebp + 8]  ; path
    mov [edi], eax
    mov eax, [ebp + 12] ; handler
    mov [edi + 4], eax
    
    inc dword [route_count]
    
    mov esp, ebp
    pop ebp
    ret

; Handle incoming HTTP request
; Parameters: request_buffer (ebp+8), response_buffer (ebp+12)
अनुरोध_संसाधय:
    push ebp
    mov ebp, esp
    
    ; Parse request line
    mov esi, [ebp + 8]  ; request buffer
    call parse_request
    
    ; Find matching route
    mov ecx, [route_count]
    mov edi, route_table
find_route:
    push ecx
    push esi
    mov esi, [edi]      ; route path
    call compare_path
    pop esi
    pop ecx
    test eax, eax
    jnz route_found
    add edi, 8
    loop find_route
    
    ; Route not found - send 404
    call send_404
    jmp handle_done
    
route_found:
    ; Call route handler
    call [edi + 4]
    
handle_done:
    mov esp, ebp
    pop ebp
    ret

; Error handlers
bind_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_bind
    mov edx, error_bind_len
    int 0x80
    ret

listen_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_listen
    mov edx, error_listen_len
    int 0x80
    ret