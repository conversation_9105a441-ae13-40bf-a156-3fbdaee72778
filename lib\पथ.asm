section .data
    ; Error messages
    error_invalid_path db 'Error: Invalid path', 0xA
    error_invalid_len equ $ - error_invalid_path
    path_separator db '\', 0

section .text
    global संयोजन     ; Join paths
    global विस्तार     ; Extension
    global नाम        ; Basename
    global निर्देशिका  ; Directory name

; Join paths function (संयोजन)
; Parameters:
;   - esi: First path
;   - edi: Second path
;   - eax: Output buffer
; Returns: eax = length of resulting path
संयोजन:
    push ebx
    push ecx
    push edx
    mov ebx, eax    ; Save output buffer

    ; Copy first path
copy_first:
    mov al, [esi]
    test al, al
    jz first_done
    mov [ebx], al
    inc esi
    inc ebx
    jmp copy_first

first_done:
    ; Add path separator if needed
    dec ebx
    cmp byte [ebx], '\'
    je check_second
    inc ebx
    mov byte [ebx], '\'
    inc ebx

check_second:
    ; Skip leading separator in second path
    cmp byte [edi], '\'
    jne copy_second
    inc edi

    ; Copy second path
copy_second:
    mov al, [edi]
    test al, al
    jz join_done
    mov [ebx], al
    inc edi
    inc ebx
    jmp copy_second

join_done:
    mov byte [ebx], 0    ; Null terminate
    sub ebx, eax         ; Calculate length
    mov eax, ebx
    pop edx
    pop ecx
    pop ebx
    ret

; Get file extension (विस्तार)
; Parameters: esi = path
; Returns: eax = pointer to extension (or null if none)
विस्तार:
    push ebx
    mov eax, esi
    xor ebx, ebx

find_ext:
    mov bl, [eax]
    test bl, bl
    jz ext_done
    cmp bl, '.'
    je found_ext
    inc eax
    jmp find_ext

found_ext:
    inc eax

ext_done:
    pop ebx
    ret

; Get basename (नाम)
; Parameters: esi = path
; Returns: eax = pointer to basename
नाम:
    push ebx
    mov eax, esi
    mov ebx, esi

find_last_sep:
    mov cl, [eax]
    test cl, cl
    jz base_done
    cmp cl, '\'
    jne next_char
    lea ebx, [eax + 1]
next_char:
    inc eax
    jmp find_last_sep

base_done:
    mov eax, ebx
    pop ebx
    ret

; Get directory name (निर्देशिका)
; Parameters: esi = path
; Returns: eax = pointer to directory name
निर्देशिका:
    push ebx
    push ecx
    mov eax, esi
    mov ebx, esi

find_last_dir_sep:
    mov cl, [eax]
    test cl, cl
    jz dir_done
    cmp cl, '\'
    jne dir_next_char
    mov byte [eax], 0    ; Temporarily null terminate
    mov ecx, eax         ; Save position
    mov eax, esi         ; Return original string
    mov ebx, ecx         ; Save end position
    jmp dir_done

dir_next_char:
    inc eax
    jmp find_last_dir_sep

dir_done:
    mov eax, esi
    pop ecx
    pop ebx
    ret