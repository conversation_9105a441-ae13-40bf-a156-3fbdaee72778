section .data
    ; Error messages
    error_div_zero db 'Error: Division by zero', 0xA
    error_div_len equ $ - error_div_zero

section .text
    global योग    ; Addition
    global शोधन  ; Subtraction
    global गुणन   ; Multiplication
    global भाग    ; Division
    global वर्ग    ; Square
    global वर्गमूल  ; Square root

; Addition function (योग)
; Parameters: eax, ebx
; Returns: eax = result
योग:
    add eax, ebx
    ret

; Subtraction function (शोधन)
; Parameters: eax, ebx
; Returns: eax = result
शोधन:
    sub eax, ebx
    ret

; Multiplication function (गुणन)
; Parameters: eax, ebx
; Returns: eax = result
गुणन:
    imul eax, ebx
    ret

; Division function (भाग)
; Parameters: eax (dividend), ebx (divisor)
; Returns: eax = quotient, edx = remainder
भाग:
    ; Check for division by zero
    test ebx, ebx
    jz div_error
    
    xor edx, edx    ; Clear edx for division
    idiv ebx
    ret

div_error:
    push eax
    mov eax, 4
    mov ebx, 2      ; stderr
    mov ecx, error_div_zero
    mov edx, error_div_len
    int 0x80
    pop eax
    mov eax, 0      ; Return 0 on error
    mov edx, 0
    ret

; Square function (वर्ग)
; Parameters: eax
; Returns: eax = result
वर्ग:
    imul eax, eax
    ret

; Square root function (वर्गमूल)
; Parameters: eax
; Returns: eax = floor(sqrt(input))
वर्गमूल:
    push ebx
    push ecx
    push edx

    mov ecx, eax    ; Save input number
    mov ebx, 0      ; Left boundary
    mov edx, eax    ; Right boundary
    inc edx

sqrt_loop:
    cmp ebx, edx
    jge sqrt_done

    push edx        ; Save edx
    add eax, ebx
    shr eax, 1      ; eax = (left + right) / 2
    
    mov edx, eax
    imul edx, eax   ; edx = mid * mid
    
    cmp edx, ecx
    pop edx         ; Restore edx
    
    je sqrt_done
    jl sqrt_low
    
    ; If mid*mid > n, adjust right boundary
    dec eax
    mov edx, eax
    jmp sqrt_loop

sqrt_low:
    ; If mid*mid < n, adjust left boundary
    inc eax
    mov ebx, eax
    jmp sqrt_loop

sqrt_done:
    pop edx
    pop ecx
    pop ebx
    ret