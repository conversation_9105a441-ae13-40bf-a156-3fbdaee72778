section .data
    ; Enhanced mathematical constants
    pi_value dd 3.14159265359
    e_value dd 2.71828182846
    golden_ratio dd 1.61803398875      ; स्वर्ण अनुपात
    sqrt_2 dd 1.41421356237            ; √2
    sqrt_3 dd 1.73205080757            ; √3
    ln_2 dd 0.69314718056              ; ln(2)
    ln_10 dd 2.30258509299             ; ln(10)

    ; Trigonometric constants
    deg_to_rad dd 0.01745329252        ; π/180
    rad_to_deg dd 57.2957795131        ; 180/π

    ; Error messages
    error_div_zero db 'त्रुटि: शून्य से भाग', 0xA
    error_div_len equ $ - error_div_zero
    error_negative_sqrt db 'त्रुटि: ऋणात्मक संख्या का वर्गमूल', 0xA
    error_domain db 'त्रुटि: डोमेन त्रुटि', 0xA
    error_overflow db 'त्रुटि: ओवरफ्लो', 0xA

section .text
    global योग    ; Addition
    global शोधन  ; Subtraction
    global गुणन   ; Multiplication
    global भाग    ; Division
    global वर्ग    ; Square
    global वर्गमूल ; Square root
    global घनमूल  ; Cube root
    global ज्या    ; Sine
    global कोज्या  ; Cosine
    global स्पर्शज्या ; Tangent
    global लघुगणक ; Logarithm
    global घातांक  ; Exponential
    global घात     ; Power
    global निरपेक्ष ; Absolute value
    global न्यूनतम ; Minimum
    global अधिकतम ; Maximum
    global फैक्टोरियल ; Factorial
    global संयोजन  ; Combination
    global क्रमचय  ; Permutation
    global वर्गमूल  ; Square root

; Addition function (योग)
; Parameters: eax, ebx
; Returns: eax = result
योग:
    add eax, ebx
    ret

; Subtraction function (शोधन)
; Parameters: eax, ebx
; Returns: eax = result
शोधन:
    sub eax, ebx
    ret

; Multiplication function (गुणन)
; Parameters: eax, ebx
; Returns: eax = result
गुणन:
    imul eax, ebx
    ret

; Division function (भाग)
; Parameters: eax (dividend), ebx (divisor)
; Returns: eax = quotient, edx = remainder
भाग:
    ; Check for division by zero
    test ebx, ebx
    jz div_error
    
    xor edx, edx    ; Clear edx for division
    idiv ebx
    ret

div_error:
    push eax
    mov eax, 4
    mov ebx, 2      ; stderr
    mov ecx, error_div_zero
    mov edx, error_div_len
    int 0x80
    pop eax
    mov eax, 0      ; Return 0 on error
    mov edx, 0
    ret

; Square function (वर्ग)
; Parameters: eax
; Returns: eax = result
वर्ग:
    imul eax, eax
    ret

; Square root function (वर्गमूल)
; Parameters: eax
; Returns: eax = floor(sqrt(input))
वर्गमूल:
    push ebx
    push ecx
    push edx

    mov ecx, eax    ; Save input number
    mov ebx, 0      ; Left boundary
    mov edx, eax    ; Right boundary
    inc edx

sqrt_loop:
    cmp ebx, edx
    jge sqrt_done

    push edx        ; Save edx
    add eax, ebx
    shr eax, 1      ; eax = (left + right) / 2
    
    mov edx, eax
    imul edx, eax   ; edx = mid * mid
    
    cmp edx, ecx
    pop edx         ; Restore edx
    
    je sqrt_done
    jl sqrt_low
    
    ; If mid*mid > n, adjust right boundary
    dec eax
    mov edx, eax
    jmp sqrt_loop

sqrt_low:
    ; If mid*mid < n, adjust left boundary
    inc eax
    mov ebx, eax
    jmp sqrt_loop

sqrt_done:
    pop edx
    pop ecx
    pop ebx
    ret