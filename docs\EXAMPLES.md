# Sanskrit Programming Language Examples

## Basic Examples

### Hello World
```sanskrit
लेखय("नमस्ते विश्व")
```

### Variable Declaration and Arithmetic
```sanskrit
परिवर्तनीय क = ५
परिवर्तनीय ख = ३
परिवर्तनीय योग = क + ख
लेखय(योग)  # Outputs ८
```

### Conditional Statements
```sanskrit
परिवर्तनीय आयु = २५
यदि आयु > १८ {
    लेखय("वयस्क")
}
```

### Loops
```sanskrit
परिवर्तनीय संख्या = ५
पुनरावर्तनम् संख्या > ० {
    लेखय(संख्या)
    संख्या = संख्या - १
}
```

### Functions
```sanskrit
कार्य गुणन(क, ख) {
    परिवर्तनीय फल = क * ख
    लेखय(फल)
}

गुणन(४, ५)  # Outputs २०
```

## Advanced Examples

### Calculator
```sanskrit
कार्य गणक(क, ख, चिह्न) {
    यदि चिह्न = "+" {
        लेखय(क + ख)
    }
    यदि चिह्न = "-" {
        लेखय(क - ख)
    }
    यदि चिह्न = "*" {
        लेखय(क * ख)
    }
}

गणक(१०, ५, "+")  # Outputs १५
```

### Number Series
```sanskrit
कार्य श्रेणी(अन्त) {
    परिवर्तनीय वर्तमान = १
    पुनरावर्तनम् वर्तमान <= अन्त {
        लेखय(वर्तमान)
        वर्तमान = वर्तमान + १
    }
}

श्रेणी(५)  # Outputs १, २, ३, ४, ५
```

### Temperature Converter
```sanskrit
कार्य तापमान_परिवर्तन(सेल्सियस) {
    परिवर्तनीय फारेनहाइट = (सेल्सियस * ९/५) + ३२
    लेखय(फारेनहाइट)
}

तापमान_परिवर्तन(३७)  # Outputs ९८.६
```

## Common Patterns

### Input Validation
```sanskrit
कार्य आयु_जांच(आयु) {
    यदि आयु < ० {
        लेखय("अमान्य आयु")
    } यदि आयु > १२० {
        लेखय("अमान्य आयु")
    } यदि आयु >= ० {
        लेखय("मान्य आयु")
    }
}
```

### Error Handling
```sanskrit
कार्य विभाजन(क, ख) {
    यदि ख = ० {
        लेखय("शून्य से विभाजन संभव नहीं")
    } यदि ख > ० {
        परिवर्तनीय फल = क / ख
        लेखय(फल)
    }
}
```

## Best Practices

1. Use meaningful variable names in Sanskrit
2. Add comments for complex logic
3. Properly indent code blocks
4. Break down complex operations into functions
5. Include appropriate error handling

## Common Issues and Solutions

1. **Syntax Errors**
   - Ensure proper use of Devanagari characters
   - Check for missing brackets or parentheses
   - Verify keyword spelling

2. **Runtime Errors**
   - Handle division by zero
   - Check array bounds
   - Validate input values

3. **Performance**
   - Optimize loop conditions
   - Use appropriate data types
   - Minimize redundant calculations