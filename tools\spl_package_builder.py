#!/usr/bin/env python3
"""
Sanskrit Programming Language Package Builder
संस्कृत प्रोग्रामिंग भाषा पैकेज निर्माता

Creates .splpkg binary distributions for multiple platforms
"""

import os
import sys
import json
import tarfile
import zipfile
import shutil
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import hashlib
import platform

class SPLPackageBuilder:
    """Build binary packages for SPL"""
    
    def __init__(self):
        self.version = "1.0.0"
        self.supported_platforms = [
            "linux-x64", "linux-x86", "linux-arm64",
            "windows-x64", "windows-x86",
            "macos-x64", "macos-arm64",
            "android-arm64", "android-arm",
            "wasm32"
        ]
        
        self.package_formats = {
            "linux": "tar.gz",
            "windows": "zip", 
            "macos": "tar.gz",
            "android": "aar",
            "wasm": "tar.gz"
        }
    
    def build_package(self, source_dir: str, output_dir: str, 
                     platforms: List[str] = None, 
                     package_type: str = "library",
                     verbose: bool = False) -> bool:
        """Build packages for specified platforms"""
        
        if platforms is None:
            platforms = self.supported_platforms
        
        # Read package configuration
        config_file = os.path.join(source_dir, "पैकेज.json")
        if not os.path.exists(config_file):
            print(f"Error: पैकेज.json not found in {source_dir}")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            package_config = json.load(f)
        
        package_name = package_config.get('name', 'unknown')
        package_version = package_config.get('version', '1.0.0')
        
        print(f"📦 Building package: {package_name} v{package_version}")
        
        success_count = 0
        total_count = len(platforms)
        
        for platform in platforms:
            if verbose:
                print(f"🔨 Building for platform: {platform}")
            
            try:
                if self.build_platform_package(
                    source_dir, output_dir, platform, package_config, 
                    package_type, verbose
                ):
                    success_count += 1
                    print(f"✅ {platform}: Success")
                else:
                    print(f"❌ {platform}: Failed")
            except Exception as e:
                print(f"❌ {platform}: Error - {e}")
        
        print(f"\n📊 Build Summary: {success_count}/{total_count} platforms successful")
        return success_count == total_count
    
    def build_platform_package(self, source_dir: str, output_dir: str,
                              platform: str, package_config: Dict,
                              package_type: str, verbose: bool) -> bool:
        """Build package for a specific platform"""
        
        package_name = package_config['name']
        package_version = package_config['version']
        
        # Create temporary build directory
        with tempfile.TemporaryDirectory(prefix=f'spl_build_{platform}_') as temp_dir:
            build_dir = os.path.join(temp_dir, 'build')
            os.makedirs(build_dir)
            
            # Compile sources for target platform
            if not self.compile_for_platform(source_dir, build_dir, platform, package_config, verbose):
                return False
            
            # Create package structure
            package_dir = os.path.join(temp_dir, 'package')
            if not self.create_package_structure(build_dir, package_dir, platform, package_config, verbose):
                return False
            
            # Generate package metadata
            self.generate_package_metadata(package_dir, platform, package_config)
            
            # Create final package archive
            package_filename = f"{package_name}-{package_version}-{platform}.splpkg"
            package_path = os.path.join(output_dir, package_filename)
            
            if not self.create_package_archive(package_dir, package_path, platform, verbose):
                return False
            
            # Generate checksums
            self.generate_checksums(package_path)
            
            if verbose:
                package_size = os.path.getsize(package_path)
                print(f"📦 Package created: {package_filename} ({package_size} bytes)")
        
        return True
    
    def compile_for_platform(self, source_dir: str, build_dir: str,
                            platform: str, package_config: Dict, verbose: bool) -> bool:
        """Compile sources for target platform"""
        
        # Find all .snkt files
        source_files = []
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                if file.endswith('.snkt'):
                    source_files.append(os.path.join(root, file))
        
        if not source_files:
            print(f"No .snkt files found in {source_dir}")
            return False
        
        # Compile each source file
        for source_file in source_files:
            rel_path = os.path.relpath(source_file, source_dir)
            output_name = os.path.splitext(rel_path)[0]
            
            if platform == "wasm32":
                output_file = os.path.join(build_dir, output_name + ".wasm")
                output_format = "wasm"
            else:
                output_file = os.path.join(build_dir, output_name + self.get_object_extension(platform))
                output_format = "object"
            
            # Create output directory
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # Compile using splc
            cmd = [
                sys.executable, "tools/splc.py",
                source_file,
                "-o", output_file,
                "-t", platform.split('-')[0],
                "-f", output_format,
                "-O", "O2"
            ]
            
            if verbose:
                cmd.append("-v")
                print(f"Compiling: {rel_path}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"Compilation failed for {rel_path}: {result.stderr}")
                return False
        
        return True
    
    def create_package_structure(self, build_dir: str, package_dir: str,
                               platform: str, package_config: Dict, verbose: bool) -> bool:
        """Create standard package directory structure"""
        
        # Create directory structure
        dirs_to_create = [
            "bin",      # Executables
            "lib",      # Libraries
            "include",  # Header files
            "docs",     # Documentation
            "examples", # Example code
            "tests"     # Test files
        ]
        
        for dir_name in dirs_to_create:
            os.makedirs(os.path.join(package_dir, dir_name), exist_ok=True)
        
        # Copy compiled artifacts
        if os.path.exists(build_dir):
            for root, dirs, files in os.walk(build_dir):
                for file in files:
                    src_path = os.path.join(root, file)
                    rel_path = os.path.relpath(src_path, build_dir)
                    
                    # Determine destination based on file type
                    if file.endswith(('.exe', '.wasm')) or (platform.startswith('linux') and '.' not in file):
                        dest_dir = "bin"
                    elif file.endswith(('.a', '.lib', '.so', '.dll', '.dylib')):
                        dest_dir = "lib"
                    elif file.endswith(('.o', '.obj')):
                        dest_dir = "lib"
                    else:
                        dest_dir = "lib"
                    
                    dest_path = os.path.join(package_dir, dest_dir, rel_path)
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    shutil.copy2(src_path, dest_path)
        
        # Copy documentation
        docs_src = os.path.join(os.path.dirname(build_dir), "..", "docs")
        if os.path.exists(docs_src):
            shutil.copytree(docs_src, os.path.join(package_dir, "docs"), dirs_exist_ok=True)
        
        # Copy examples
        examples_src = os.path.join(os.path.dirname(build_dir), "..", "examples")
        if os.path.exists(examples_src):
            shutil.copytree(examples_src, os.path.join(package_dir, "examples"), dirs_exist_ok=True)
        
        # Copy tests
        tests_src = os.path.join(os.path.dirname(build_dir), "..", "tests")
        if os.path.exists(tests_src):
            shutil.copytree(tests_src, os.path.join(package_dir, "tests"), dirs_exist_ok=True)
        
        return True
    
    def generate_package_metadata(self, package_dir: str, platform: str, package_config: Dict):
        """Generate package metadata files"""
        
        # Create package manifest
        manifest = {
            "name": package_config['name'],
            "version": package_config['version'],
            "description": package_config.get('description', ''),
            "author": package_config.get('author', ''),
            "platform": platform,
            "spl_version": "1.0.0",
            "dependencies": package_config.get('dependencies', {}),
            "files": self.list_package_files(package_dir),
            "created_at": self.get_current_timestamp()
        }
        
        manifest_path = os.path.join(package_dir, "MANIFEST.json")
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        # Create installation script
        install_script = self.generate_install_script(platform, package_config)
        script_ext = ".bat" if platform.startswith("windows") else ".sh"
        script_path = os.path.join(package_dir, f"install{script_ext}")
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(install_script)
        
        # Make script executable on Unix-like systems
        if not platform.startswith("windows"):
            os.chmod(script_path, 0o755)
        
        # Create README
        readme_content = self.generate_readme(package_config)
        readme_path = os.path.join(package_dir, "README.md")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    def create_package_archive(self, package_dir: str, package_path: str,
                             platform: str, verbose: bool) -> bool:
        """Create the final package archive"""
        
        os.makedirs(os.path.dirname(package_path), exist_ok=True)
        
        if platform.startswith("windows"):
            # Create ZIP archive for Windows
            with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                for root, dirs, files in os.walk(package_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, package_dir)
                        zf.write(file_path, arc_path)
        else:
            # Create tar.gz archive for Unix-like systems
            with tarfile.open(package_path, 'w:gz') as tf:
                tf.add(package_dir, arcname='.')
        
        return True
    
    def generate_checksums(self, package_path: str):
        """Generate checksums for the package"""
        
        # Calculate SHA256 checksum
        sha256_hash = hashlib.sha256()
        with open(package_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        checksum_file = package_path + ".sha256"
        with open(checksum_file, 'w') as f:
            f.write(f"{sha256_hash.hexdigest()}  {os.path.basename(package_path)}\n")
    
    def get_object_extension(self, platform: str) -> str:
        """Get object file extension for platform"""
        if platform.startswith("windows"):
            return ".obj"
        else:
            return ".o"
    
    def list_package_files(self, package_dir: str) -> List[str]:
        """List all files in the package"""
        files = []
        for root, dirs, filenames in os.walk(package_dir):
            for filename in filenames:
                file_path = os.path.join(root, filename)
                rel_path = os.path.relpath(file_path, package_dir)
                files.append(rel_path)
        return sorted(files)
    
    def get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.utcnow().isoformat() + 'Z'
    
    def generate_install_script(self, platform: str, package_config: Dict) -> str:
        """Generate installation script"""
        if platform.startswith("windows"):
            return f"""@echo off
echo Installing {package_config['name']} v{package_config['version']}
echo Platform: {platform}

REM Copy files to SPL installation directory
if not exist "%SPL_HOME%" (
    echo Error: SPL_HOME environment variable not set
    exit /b 1
)

xcopy /E /I /Y bin "%SPL_HOME%\\bin\\"
xcopy /E /I /Y lib "%SPL_HOME%\\lib\\"
xcopy /E /I /Y include "%SPL_HOME%\\include\\"

echo Installation complete!
"""
        else:
            return f"""#!/bin/bash
echo "Installing {package_config['name']} v{package_config['version']}"
echo "Platform: {platform}"

# Copy files to SPL installation directory
if [ -z "$SPL_HOME" ]; then
    echo "Error: SPL_HOME environment variable not set"
    exit 1
fi

cp -r bin/* "$SPL_HOME/bin/" 2>/dev/null || true
cp -r lib/* "$SPL_HOME/lib/" 2>/dev/null || true
cp -r include/* "$SPL_HOME/include/" 2>/dev/null || true

echo "Installation complete!"
"""
    
    def generate_readme(self, package_config: Dict) -> str:
        """Generate README content"""
        return f"""# {package_config['name']}

{package_config.get('description', 'A Sanskrit Programming Language package')}

## Version
{package_config['version']}

## Author
{package_config.get('author', 'Unknown')}

## Installation

### Using SPL Package Manager
```bash
spl install {package_config['name']}
```

### Manual Installation
1. Extract the package archive
2. Run the installation script:
   - Linux/macOS: `./install.sh`
   - Windows: `install.bat`

## Dependencies
{json.dumps(package_config.get('dependencies', {}), indent=2)}

## Usage
See the examples directory for usage examples.

## Documentation
See the docs directory for detailed documentation.

---
Generated by SPL Package Builder
"""

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Sanskrit Programming Language Package Builder')
    parser.add_argument('source_dir', help='Source directory containing पैकेज.json')
    parser.add_argument('-o', '--output', default='dist', help='Output directory for packages')
    parser.add_argument('-p', '--platforms', nargs='+', help='Target platforms')
    parser.add_argument('-t', '--type', choices=['library', 'application'], 
                       default='library', help='Package type')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    builder = SPLPackageBuilder()
    
    success = builder.build_package(
        source_dir=args.source_dir,
        output_dir=args.output,
        platforms=args.platforms,
        package_type=args.type,
        verbose=args.verbose
    )
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
