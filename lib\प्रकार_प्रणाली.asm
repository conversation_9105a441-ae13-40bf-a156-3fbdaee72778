section .data
    ; Type system keywords in Sanskrit
    kw_type db 'प्रकार', 0            ; Type
    kw_interface db 'अन्तराफलक', 0    ; Interface
    kw_generic db 'सामान्य', 0         ; Generic
    kw_extends db 'विस्तार', 0         ; Extends
    kw_implements db 'कार्यान्वयन', 0   ; Implements
    
    ; Type system keywords in Hindi
    kw_type_hi db 'टाइप', 0           ; Type in Hindi
    kw_interface_hi db 'इंटरफेस', 0    ; Interface in Hindi
    kw_generic_hi db 'जेनेरिक', 0      ; Generic in Hindi
    kw_extends_hi db 'विस्तृत', 0      ; Extends in Hindi
    kw_implements_hi db 'लागू', 0      ; Implements in Hindi
    
    ; Built-in types in Sanskrit
    type_int db 'पूर्णाङ्क', 0         ; Integer
    type_float db 'दशांश', 0           ; Float
    type_string db 'वाक्य', 0          ; String
    type_bool db 'बूलियन', 0           ; Boolean
    type_array db 'श्रेणी', 0           ; Array
    
    ; Built-in types in Hindi
    type_int_hi db 'पूर्णांक', 0       ; Integer in Hindi
    type_float_hi db 'दशमलव', 0       ; Float in Hindi
    type_string_hi db 'शब्द', 0        ; String in Hindi
    type_bool_hi db 'सत्य_मिथ्या', 0    ; Boolean in Hindi
    type_array_hi db 'सरणी', 0         ; Array in Hindi
    
    ; Type error messages in Sanskrit
    type_error db 'प्रकार त्रुटि:', 0
    type_mismatch db 'प्रकार मेल नहीं:', 0
    undefined_type db 'अपरिभाषित प्रकार:', 0
    generic_error db 'सामान्य प्रकार त्रुटि:', 0
    
    ; Type error messages in Hindi
    type_error_hi db 'टाइप त्रुटि:', 0
    type_mismatch_hi db 'टाइप मिलान नहीं:', 0
    undefined_type_hi db 'अपरिभाषित टाइप:', 0
    generic_error_hi db 'जेनेरिक टाइप त्रुटि:', 0

section .bss
    ; Type system data structures
    type_table resb 1024        ; Table for storing type definitions
    type_count resd 1           ; Counter for number of types
    generic_table resb 512      ; Table for generic type parameters
    generic_count resd 1        ; Counter for generic parameters
    interface_table resb 1024   ; Table for interface definitions
    interface_count resd 1      ; Counter for interfaces

section .text
    global init_type_system
    global check_type
    global infer_type
    global define_type
    global define_interface
    global implement_interface

; Initialize the type system
init_type_system:
    push ebp
    mov ebp, esp
    
    ; Initialize counters
    mov dword [type_count], 0
    mov dword [generic_count], 0
    mov dword [interface_count], 0
    
    ; Initialize built-in types
    call init_builtin_types
    
    mov esp, ebp
    pop ebp
    ret

; Initialize built-in types
init_builtin_types:
    push ebp
    mov ebp, esp
    
    ; Add integer type
    push type_int
    call add_builtin_type
    push type_int_hi
    call add_builtin_type
    
    ; Add float type
    push type_float
    call add_builtin_type
    push type_float_hi
    call add_builtin_type
    
    ; Add string type
    push type_string
    call add_builtin_type
    push type_string_hi
    call add_builtin_type
    
    ; Add boolean type
    push type_bool
    call add_builtin_type
    push type_bool_hi
    call add_builtin_type
    
    ; Add array type
    push type_array
    call add_builtin_type
    push type_array_hi
    call add_builtin_type
    
    mov esp, ebp
    pop ebp
    ret

; Check type compatibility
check_type:
    push ebp
    mov ebp, esp
    
    ; Get parameters
    mov eax, [ebp + 8]     ; First type
    mov ebx, [ebp + 12]    ; Second type
    
    ; Compare types
    push eax
    push ebx
    call compare_types
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret

; Infer type from expression
infer_type:
    push ebp
    mov ebp, esp
    
    ; Get expression
    mov eax, [ebp + 8]
    
    ; Analyze expression
    push eax
    call analyze_expression
    add esp, 4
    
    mov esp, ebp
    pop ebp
    ret

; Define custom type
define_type:
    push ebp
    mov ebp, esp
    
    ; Get type name and definition
    mov eax, [ebp + 8]     ; Type name
    mov ebx, [ebp + 12]    ; Type definition
    
    ; Add to type table
    push eax
    push ebx
    call add_type_definition
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret

; Define interface
define_interface:
    push ebp
    mov ebp, esp
    
    ; Get interface name and methods
    mov eax, [ebp + 8]     ; Interface name
    mov ebx, [ebp + 12]    ; Interface methods
    
    ; Add to interface table
    push eax
    push ebx
    call add_interface_definition
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret

; Implement interface
implement_interface:
    push ebp
    mov ebp, esp
    
    ; Get type and interface
    mov eax, [ebp + 8]     ; Type name
    mov ebx, [ebp + 12]    ; Interface name
    
    ; Verify implementation
    push eax
    push ebx
    call verify_implementation
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret