section .data
    ; Type system keywords in Sanskrit
    kw_type db 'प्रकार', 0            ; Type
    kw_interface db 'अन्तराफलक', 0    ; Interface
    kw_generic db 'सामान्य', 0         ; Generic
    kw_extends db 'विस्तार', 0         ; Extends
    kw_implements db 'कार्यान्वयन', 0   ; Implements
    
    ; Type system keywords in Hindi
    kw_type_hi db 'टाइप', 0           ; Type in Hindi
    kw_interface_hi db 'इंटरफेस', 0    ; Interface in Hindi
    kw_generic_hi db 'जेनेरिक', 0      ; Generic in Hindi
    kw_extends_hi db 'विस्तृत', 0      ; Extends in Hindi
    kw_implements_hi db 'लागू', 0      ; Implements in Hindi
    
    ; Enhanced built-in types in Sanskrit
    type_int db 'पूर्णाङ्क', 0         ; Integer
    type_float db 'दशांश', 0           ; Float
    type_string db 'वाक्य', 0          ; String
    type_bool db 'बूलियन', 0           ; Boolean
    type_array db 'श्रेणी', 0           ; Array
    type_number db 'संख्या', 0         ; Number (new)
    type_panktih db 'पङ्क्तिः', 0      ; String (alternative)
    type_suchi db 'सूची', 0            ; List
    type_karya db 'कार्य', 0           ; Function
    type_shunya db 'शून्य', 0          ; Null/Void
    type_booleanah db 'बूलियनः', 0     ; Boolean (alternative)

    ; Type constants
    TYPE_UNKNOWN equ 0
    TYPE_NUMBER equ 1           ; संख्या
    TYPE_STRING equ 2           ; पङ्क्तिः
    TYPE_BOOLEAN equ 3          ; बूलियनः
    TYPE_LIST equ 4             ; सूची
    TYPE_FUNCTION equ 5         ; कार्य
    TYPE_OBJECT equ 6           ; वस्तु
    TYPE_NULL equ 7             ; शून्य
    TYPE_GENERIC equ 8          ; सामान्य
    TYPE_UNION equ 9            ; संघ
    TYPE_OPTIONAL equ 10        ; वैकल्पिक
    
    ; Built-in types in Hindi
    type_int_hi db 'पूर्णांक', 0       ; Integer in Hindi
    type_float_hi db 'दशमलव', 0       ; Float in Hindi
    type_string_hi db 'शब्द', 0        ; String in Hindi
    type_bool_hi db 'सत्य_मिथ्या', 0    ; Boolean in Hindi
    type_array_hi db 'सरणी', 0         ; Array in Hindi
    
    ; Enhanced type error messages in Sanskrit
    type_error db 'प्रकार त्रुटि:', 0
    type_mismatch db 'प्रकार मेल नहीं:', 0
    error_type_mismatch db 'त्रुटि: प्रकार मेल नहीं खाता', 0xA
    error_undefined_type db 'त्रुटि: अपरिभाषित प्रकार', 0xA
    error_invalid_operation db 'त्रुटि: अवैध संचालन', 0xA
    error_null_assignment db 'त्रुटि: शून्य असाइनमेंट', 0xA

    ; Type annotation symbols
    kw_type_annotation db ':', 0    ; Type annotation separator
    kw_optional db '?', 0           ; Optional type marker
    kw_union db '|', 0              ; Union type separator
    kw_generic_open db '<', 0       ; Generic type opening
    kw_generic_close db '>', 0      ; Generic type closing

section .bss
    ; Enhanced type checking state
    type_stack resb 2048            ; Type inference stack
    type_sp resd 1                  ; Type stack pointer
    current_type resd 1             ; Current expression type
    type_errors resb 4096           ; Type error messages
    type_table resb 8192            ; Type symbol table
    type_table_size resd 1          ; Size of type table
    generic_types resb 1024         ; Generic type definitions
    union_types resb 2048           ; Union type definitions
    optional_types resb 1024        ; Optional type tracking

section .text
    global प्रकार_जांच_प्रारम्भ        ; type_check_init
    global प्रकार_अनुमान              ; infer_type
    global प्रकार_संगतता_जांच          ; check_type_compatibility
    global प्रकार_त्रुटि               ; type_error
    global प्रकार_एनोटेशन_पार्स        ; parse_type_annotation
    global सामान्य_प्रकार_बनाएं        ; create_generic_type
    global संघ_प्रकार_बनाएं           ; create_union_type
    global वैकल्पिक_प्रकार_बनाएं       ; create_optional_type
    global प्रकार_रूपांतरण            ; type_conversion
    global प्रकार_जांच_कार्य           ; type_check_function

; Initialize type checking system
प्रकार_जांच_प्रारम्भ:
    push ebp
    mov ebp, esp

    ; Initialize type stack pointer
    mov dword [type_sp], 0

    ; Initialize type table size
    mov dword [type_table_size], 0

    ; Clear current type
    mov dword [current_type], TYPE_UNKNOWN

    mov esp, ebp
    pop ebp
    ret

; Infer type from expression
; Parameters: expression_address
; Returns: inferred type
प्रकार_अनुमान:
    push ebp
    mov ebp, esp
    push ebx
    push esi

    mov esi, [ebp + 8]      ; expression address

    ; Check expression type
    mov al, [esi]           ; First byte indicates expression type

    cmp al, 1               ; Literal number
    je .number_type

    cmp al, 2               ; String literal
    je .string_type

    cmp al, 3               ; Boolean literal
    je .boolean_type

    cmp al, 4               ; List literal
    je .list_type

    cmp al, 5               ; Function call
    je .function_type

    ; Default: unknown type
    mov eax, TYPE_UNKNOWN
    jmp .done

.number_type:
    mov eax, TYPE_NUMBER
    jmp .done

.string_type:
    mov eax, TYPE_STRING
    jmp .done

.boolean_type:
    mov eax, TYPE_BOOLEAN
    jmp .done

.list_type:
    mov eax, TYPE_LIST
    jmp .done

.function_type:
    mov eax, TYPE_FUNCTION

.done:
    mov [current_type], eax

    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Check type compatibility
; Parameters: type1, type2
; Returns: 1 if compatible, 0 if not
प्रकार_संगतता_जांच:
    push ebp
    mov ebp, esp

    mov eax, [ebp + 8]      ; type1
    mov ebx, [ebp + 12]     ; type2

    ; Exact match
    cmp eax, ebx
    je .compatible

    ; Check for implicit conversions
    cmp eax, TYPE_NUMBER
    je .check_number_conversion

    cmp eax, TYPE_STRING
    je .check_string_conversion

    ; Default: incompatible
    xor eax, eax
    jmp .done

.check_number_conversion:
    ; Numbers can be converted to strings
    cmp ebx, TYPE_STRING
    je .compatible
    jmp .incompatible

.check_string_conversion:
    ; Strings can be converted to booleans (empty = false)
    cmp ebx, TYPE_BOOLEAN
    je .compatible
    jmp .incompatible

.compatible:
    mov eax, 1
    jmp .done

.incompatible:
    xor eax, eax

.done:
    mov esp, ebp
    pop ebp
    ret
    undefined_type db 'अपरिभाषित प्रकार:', 0
    generic_error db 'सामान्य प्रकार त्रुटि:', 0
    
    ; Type error messages in Hindi
    type_error_hi db 'टाइप त्रुटि:', 0
    type_mismatch_hi db 'टाइप मिलान नहीं:', 0
    undefined_type_hi db 'अपरिभाषित टाइप:', 0
    generic_error_hi db 'जेनेरिक टाइप त्रुटि:', 0

section .bss
    ; Type system data structures
    type_table resb 1024        ; Table for storing type definitions
    type_count resd 1           ; Counter for number of types
    generic_table resb 512      ; Table for generic type parameters
    generic_count resd 1        ; Counter for generic parameters
    interface_table resb 1024   ; Table for interface definitions
    interface_count resd 1      ; Counter for interfaces

section .text
    global init_type_system
    global check_type
    global infer_type
    global define_type
    global define_interface
    global implement_interface

; Initialize the type system
init_type_system:
    push ebp
    mov ebp, esp
    
    ; Initialize counters
    mov dword [type_count], 0
    mov dword [generic_count], 0
    mov dword [interface_count], 0
    
    ; Initialize built-in types
    call init_builtin_types
    
    mov esp, ebp
    pop ebp
    ret

; Initialize built-in types
init_builtin_types:
    push ebp
    mov ebp, esp
    
    ; Add integer type
    push type_int
    call add_builtin_type
    push type_int_hi
    call add_builtin_type
    
    ; Add float type
    push type_float
    call add_builtin_type
    push type_float_hi
    call add_builtin_type
    
    ; Add string type
    push type_string
    call add_builtin_type
    push type_string_hi
    call add_builtin_type
    
    ; Add boolean type
    push type_bool
    call add_builtin_type
    push type_bool_hi
    call add_builtin_type
    
    ; Add array type
    push type_array
    call add_builtin_type
    push type_array_hi
    call add_builtin_type
    
    mov esp, ebp
    pop ebp
    ret

; Check type compatibility
check_type:
    push ebp
    mov ebp, esp
    
    ; Get parameters
    mov eax, [ebp + 8]     ; First type
    mov ebx, [ebp + 12]    ; Second type
    
    ; Compare types
    push eax
    push ebx
    call compare_types
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret

; Infer type from expression
infer_type:
    push ebp
    mov ebp, esp
    
    ; Get expression
    mov eax, [ebp + 8]
    
    ; Analyze expression
    push eax
    call analyze_expression
    add esp, 4
    
    mov esp, ebp
    pop ebp
    ret

; Define custom type
define_type:
    push ebp
    mov ebp, esp
    
    ; Get type name and definition
    mov eax, [ebp + 8]     ; Type name
    mov ebx, [ebp + 12]    ; Type definition
    
    ; Add to type table
    push eax
    push ebx
    call add_type_definition
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret

; Define interface
define_interface:
    push ebp
    mov ebp, esp
    
    ; Get interface name and methods
    mov eax, [ebp + 8]     ; Interface name
    mov ebx, [ebp + 12]    ; Interface methods
    
    ; Add to interface table
    push eax
    push ebx
    call add_interface_definition
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret

; Implement interface
implement_interface:
    push ebp
    mov ebp, esp
    
    ; Get type and interface
    mov eax, [ebp + 8]     ; Type name
    mov ebx, [ebp + 12]    ; Interface name
    
    ; Verify implementation
    push eax
    push ebx
    call verify_implementation
    add esp, 8
    
    mov esp, ebp
    pop ebp
    ret