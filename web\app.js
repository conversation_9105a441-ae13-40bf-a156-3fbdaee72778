// Load WebAssembly module
const wasmModule = import('./sanskrit.wasm.js');

// Initialize CodeMirror editor
const editor = CodeMirror.fromTextArea(document.getElementById('editor'), {
    mode: 'sanskrit',
    theme: 'monokai',
    lineNumbers: true,
    autoCloseBrackets: true,
    matchBrackets: true,
    indentUnit: 4,
    tabSize: 4,
    indentWithTabs: true,
    lineWrapping: true,
    gutters: ['CodeMirror-linenumbers', 'breakpoints'],
    foldGutter: true
});

// Add breakpoint handling
const breakpoints = [];
editor.on('gutterClick', (cm, n) => {
    const info = cm.lineInfo(n);
    const marker = info.gutterMarkers ? info.gutterMarkers.breakpoints : null;
    cm.setGutterMarker(n, 'breakpoints', marker ? null : makeMarker());
    const idx = breakpoints.indexOf(n);
    if (idx > -1) {
        breakpoints.splice(idx, 1);
    } else {
        breakpoints.push(n);
    }
});

function makeMarker() {
    const marker = document.createElement('div');
    marker.style.color = '#822';
    marker.style.marginLeft = '5px';
    marker.innerHTML = '●';
    return marker;
}

// Define custom mode for Sanskrit syntax highlighting with more keywords and operators
CodeMirror.defineMode('sanskrit', function() {
    const keywords = [
        'परिवर्तनीय', 'लेखय', 'यदि', 'पुनरावर्तनम्', 'कार्य', 'पठ', 'टिप्पणी',
        'अन्यथा', 'समाप्त', 'वापस', 'तथा', 'वा', 'च'
    ];
    const operators = ['+', '-', '*', '/', '=', '==', '!=', '<', '>', '<=', '>=']; 

    return {
        token: function(stream) {
            // Handle comments
            if (stream.match('//')) {
                stream.skipToEnd();
                return 'comment';
            }

            // Handle strings
            if (stream.match('"')) {
                while (!stream.eol() && stream.next() != '"') {}
                return 'string';
            }

            // Handle keywords
            for (let keyword of keywords) {
                if (stream.match(keyword)) return 'keyword';
            }

            // Handle numbers (Devanagari and Arabic numerals)
            if (stream.match(/[०-९]+/) || stream.match(/\d+/)) return 'number';

            // Handle operators
            for (let op of operators) {
                if (stream.match(op)) return 'operator';
            }

            stream.next();
            return null;
        }
    };
});

// Variable inspection panel
const variablePanel = document.createElement('div');
variablePanel.className = 'variable-panel';
variablePanel.style.cssText = 'position: fixed; right: 20px; top: 100px; width: 200px; background: #2d2d2d; color: white; padding: 10px; border-radius: 4px;';
document.body.appendChild(variablePanel);

// Get DOM elements
const runBtn = document.getElementById('runBtn');
const exportBtn = document.getElementById('exportBtn');
const clearBtn = document.getElementById('clearBtn');
const output = document.getElementById('output');

// Sample code for testing
const sampleCode = `परिवर्तनीय x = १०
लेखय(x)
`;
editor.setValue(sampleCode);

// Function to run the code with debugging support
runBtn.addEventListener('click', async () => {
    const code = editor.getValue();
    output.innerHTML += `<div style="color: #4CAF50">Executing code:</div>`;
    output.innerHTML += `<pre>${code}</pre>`;
    
    try {
        const variables = {};
        const lines = code.split('\n');
        for (let i = 0; i < lines.length; i++) {
            if (breakpoints.includes(i)) {
                updateVariablePanel(variables);
                await new Promise(resolve => {
                    const continueBtn = document.createElement('button');
                    continueBtn.textContent = 'Continue';
                    continueBtn.onclick = resolve;
                    output.appendChild(continueBtn);
                });
            }
            // Execute line and update variables
            const result = await executeSanskritCode(lines[i], variables);
            if (result !== undefined) {
                output.innerHTML += `<div>Result: ${result}</div>`;
            }
        }
    } catch (error) {
        output.innerHTML += `<div style="color: #f44336">Error: ${error.message}</div>`;
    }
    
    output.scrollTop = output.scrollHeight;
});

// Function to update variable inspection panel
function updateVariablePanel(variables) {
    variablePanel.innerHTML = '<h4>Variables</h4>';
    for (const [name, value] of Object.entries(variables)) {
        variablePanel.innerHTML += `<div>${name}: ${value}</div>`;
    }
}

// Function to export code as .snkt file
exportBtn.addEventListener('click', () => {
    const code = editor.getValue();
    const blob = new Blob([code], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'code.snkt';
    a.click();
    window.URL.revokeObjectURL(url);
});

// Function to clear output
clearBtn.addEventListener('click', () => {
    output.innerHTML = '';
});

// Add keyboard shortcuts
editor.setOption('extraKeys', {
    'Ctrl-Enter': function() {
        runBtn.click();
    },
    'Ctrl-S': function(cm) {
        exportBtn.click();
        return false;
    }
});

// Handle file upload
const fileInput = document.getElementById('fileInput');
const uploadBtn = document.getElementById('uploadBtn');

uploadBtn.addEventListener('click', () => {
    fileInput.click();
});

fileInput.addEventListener('change', (event) => {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            editor.setValue(e.target.result);
            output.innerHTML += `<div style="color: #4CAF50">Loaded file: ${file.name}</div>`;
            output.scrollTop = output.scrollHeight;
        };
        reader.readAsText(file);
    }
});

// Enhance output window with timestamps and formatting
function appendOutput(text, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        info: '#fff',
        success: '#4CAF50',
        error: '#f44336',
        code: '#ffd700'
    };
    
    output.innerHTML += `
        <div style="margin-bottom: 8px">
            <span style="color: #888">[${timestamp}]</span>
            <span style="color: ${colors[type]}">${text}</span>
        </div>
    `;
    output.scrollTop = output.scrollHeight;
}

// Update run button to use enhanced output
runBtn.addEventListener('click', async () => {
    const code = editor.getValue();
    appendOutput('Executing code:', 'info');
    appendOutput(code, 'code');
    
    try {
        const result = await executeSanskritCode(code);
        if (result !== undefined) {
            appendOutput(`Result: ${result}`, 'success');
        }
    } catch (error) {
        appendOutput(`Error: ${error.message}`, 'error');
    }
});

// Add keyboard shortcuts
editor.setOption('extraKeys', {
    'Ctrl-Enter': function() {
        runBtn.click();
    },
    'Ctrl-S': function(cm) {
        exportBtn.click();
        return false;
    }
});