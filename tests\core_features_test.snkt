# Core Features Test Suite

# Test 1: Basic Print
लेखय("नमस्ते जगत्")

# Test 2: Variable Declaration and Assignment
परिवर्तनीय अ = १०
लेखय(अ)

# Test 3: Arithmetic Operations
परिवर्तनीय ब = २०
परिवर्तनीय योग = अ + ब
परिवर्तनीय अंतर = ब - अ
परिवर्तनीय गुणन = अ * ब
परिवर्तनीय भाग = ब / अ

लेखय(योग)    # Should print ३०
लेखय(अंतर)   # Should print १०
लेखय(गुणन)   # Should print २००
लेखय(भाग)    # Should print २

# Test 4: Conditional Statements
यदि अ > ब:
    लेखय("अ बृहत्तर अस्ति")
अन्यथा:
    लेखय("ब बृहत्तर अस्ति")

# Test 5: Comparison Operators
परिवर्तनीय क = १०
परिवर्तनीय ख = १०

यदि क == ख:
    लेखय("समान")

यदि क != ब:
    लेखय("असमान")

यदि क <= ख:
    लेखय("लघु वा समान")

यदि क >= ख:
    लेखय("बृहत् वा समान")

# Test 6: Comments
# एषः एकः टिप्पणी अस्ति
लेखय("टिप्पणी परीक्षणम्")  # This is an inline comment