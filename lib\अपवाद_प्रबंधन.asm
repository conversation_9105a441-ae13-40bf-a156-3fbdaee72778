; Exception Handling Implementation for Sanskrit Programming Language
; प्रयत्नः (prayatnah) - Try block
; ग्रहणम् (grahanam) - Catch block
; त्रुटि (truti) - Error/Exception
; अन्ततः (antatah) - Finally block
; फेंकना (phenkana) - Throw statement

section .data
    ; Exception handling keywords
    kw_prayatn db 'प्रयत्नः', 0        ; try
    kw_grahan db 'ग्रहणम्', 0          ; catch
    kw_truti db 'त्रुटि', 0            ; error
    kw_antata db 'अन्ततः', 0           ; finally
    kw_phenkana db 'फेंकना', 0         ; throw
    
    ; Exception types
    EXCEPTION_RUNTIME equ 1
    EXCEPTION_TYPE equ 2
    EXCEPTION_ARITHMETIC equ 3
    EXCEPTION_NULL equ 4
    EXCEPTION_INDEX equ 5
    EXCEPTION_CUSTOM equ 6
    
    ; Error messages
    error_uncaught db 'त्रुटि: अपकड़ा गया अपवाद: ', 0
    error_stack_overflow db 'त्रुटि: स्टैक ओवरफ्लो', 0xA
    error_null_reference db 'त्रुटि: शून्य संदर्भ', 0xA
    error_division_zero db 'त्रुटि: शून्य से भाग', 0xA
    error_index_bounds db 'त्रुटि: सूचकांक सीमा से बाहर', 0xA

section .bss
    ; Exception handling stack
    exception_stack resb 2048       ; Exception handler stack
    exception_sp resd 1             ; Exception stack pointer
    current_exception resb 256      ; Current exception data
    exception_message resb 512      ; Exception message buffer
    finally_blocks resb 1024        ; Finally block addresses
    finally_count resd 1            ; Number of finally blocks
    
    ; Exception context
    exception_type resd 1           ; Type of current exception
    exception_line resd 1           ; Line number where exception occurred
    exception_file resb 256         ; File name where exception occurred

section .text
    global प्रयत्न_प्रारम्भ           ; try_begin
    global ग्रहण_प्रारम्भ            ; catch_begin
    global अन्तत_प्रारम्भ            ; finally_begin
    global अपवाद_फेंकना             ; throw_exception
    global अपवाद_प्रबंधक_स्थापना      ; setup_exception_handler
    global अपवाद_सफाई              ; cleanup_exception
    global अपवाद_संदेश_सेट          ; set_exception_message

; Initialize exception handling system
प्रयत्न_प्रारम्भ:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    
    ; Get current exception stack pointer
    mov eax, [exception_sp]
    
    ; Push current context onto exception stack
    mov ebx, exception_stack
    add ebx, eax
    
    ; Store return address and stack pointer
    mov edx, [ebp + 4]      ; return address
    mov [ebx], edx
    mov [ebx + 4], esp      ; current stack pointer
    mov [ebx + 8], ebp      ; current base pointer
    
    ; Update exception stack pointer
    add eax, 16
    mov [exception_sp], eax
    
    ; Check for stack overflow
    cmp eax, 2048
    jge .stack_overflow
    
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret
    
.stack_overflow:
    ; Handle stack overflow
    mov eax, 4
    mov ebx, 2              ; stderr
    mov ecx, error_stack_overflow
    mov edx, 23
    int 0x80
    
    ; Exit program
    mov eax, 1
    mov ebx, 1
    int 0x80

; Begin catch block
; Parameters: exception_type_filter
ग्रहण_प्रारम्भ:
    push ebp
    mov ebp, esp
    
    ; Get exception type filter
    mov eax, [ebp + 8]
    
    ; Check if current exception matches filter
    mov ebx, [exception_type]
    cmp eax, ebx
    je .handle_exception
    
    ; Exception doesn't match, continue unwinding
    jmp .continue_unwinding
    
.handle_exception:
    ; Clear exception state
    mov dword [exception_type], 0
    
    ; Exception is handled
    mov eax, 1
    jmp .done
    
.continue_unwinding:
    ; Continue looking for handler
    mov eax, 0
    
.done:
    mov esp, ebp
    pop ebp
    ret

; Begin finally block
अन्तत_प्रारम्भ:
    push ebp
    mov ebp, esp
    
    ; Get finally block address
    mov eax, [ebp + 8]
    
    ; Add to finally blocks list
    mov ebx, [finally_count]
    mov ecx, finally_blocks
    mov [ecx + ebx * 4], eax
    
    ; Increment finally count
    inc dword [finally_count]
    
    mov esp, ebp
    pop ebp
    ret

; Throw an exception
; Parameters: exception_type, message
अपवाद_फेंकना:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    ; Store exception type
    mov eax, [ebp + 8]
    mov [exception_type], eax
    
    ; Store exception message
    mov esi, [ebp + 12]     ; message
    mov edi, exception_message
    mov ecx, 512
    
.copy_message:
    lodsb
    stosb
    test al, al
    jz .message_copied
    loop .copy_message
    
.message_copied:
    ; Start exception unwinding
    call unwind_stack
    
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Unwind the stack looking for exception handlers
unwind_stack:
    push ebp
    mov ebp, esp
    
    ; Check if there are any exception handlers
    mov eax, [exception_sp]
    cmp eax, 0
    je .no_handler
    
    ; Get the top exception handler
    sub eax, 16
    mov [exception_sp], eax
    
    mov ebx, exception_stack
    add ebx, eax
    
    ; Restore context
    mov edx, [ebx]          ; return address
    mov esp, [ebx + 4]      ; stack pointer
    mov ebp, [ebx + 8]      ; base pointer
    
    ; Jump to exception handler
    jmp edx
    
.no_handler:
    ; No handler found, print uncaught exception
    mov eax, 4
    mov ebx, 2              ; stderr
    mov ecx, error_uncaught
    mov edx, 35
    int 0x80
    
    ; Print exception message
    mov eax, 4
    mov ebx, 2
    mov ecx, exception_message
    mov edx, 512
    int 0x80
    
    ; Exit program
    mov eax, 1
    mov ebx, 1
    int 0x80
    
    mov esp, ebp
    pop ebp
    ret

; Setup exception handler for specific exception types
अपवाद_प्रबंधक_स्थापना:
    push ebp
    mov ebp, esp
    
    ; This would set up handlers for different exception types
    ; Implementation depends on specific requirements
    
    mov esp, ebp
    pop ebp
    ret

; Cleanup exception handling state
अपवाद_सफाई:
    push ebp
    mov ebp, esp
    push ebx
    push ecx
    
    ; Execute all finally blocks
    mov ecx, [finally_count]
    mov ebx, finally_blocks
    
.finally_loop:
    cmp ecx, 0
    je .finally_done
    
    ; Execute finally block
    mov eax, [ebx]
    call eax
    
    add ebx, 4
    dec ecx
    jmp .finally_loop
    
.finally_done:
    ; Clear finally count
    mov dword [finally_count], 0
    
    ; Clear exception state
    mov dword [exception_type], 0
    
    pop ecx
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Set exception message
; Parameters: message
अपवाद_संदेश_सेट:
    push ebp
    mov ebp, esp
    push esi
    push edi
    
    mov esi, [ebp + 8]      ; message
    mov edi, exception_message
    mov ecx, 512
    
.copy_loop:
    lodsb
    stosb
    test al, al
    jz .done
    loop .copy_loop
    
.done:
    pop edi
    pop esi
    mov esp, ebp
    pop ebp
    ret

; Built-in exception throwers for common errors
throw_null_reference:
    push error_null_reference
    push EXCEPTION_NULL
    call अपवाद_फेंकना
    add esp, 8
    ret

throw_division_by_zero:
    push error_division_zero
    push EXCEPTION_ARITHMETIC
    call अपवाद_फेंकना
    add esp, 8
    ret

throw_index_out_of_bounds:
    push error_index_bounds
    push EXCEPTION_INDEX
    call अपवाद_फेंकना
    add esp, 8
    ret
