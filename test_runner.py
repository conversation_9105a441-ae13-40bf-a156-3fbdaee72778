#!/usr/bin/env python3
import subprocess
import os
import sys
import json
from datetime import datetime

class SanskritTestRunner:
    def __init__(self):
        self.test_results = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'tests': []
        }
        self.test_dir = 'tests'
        if not os.path.exists(self.test_dir):
            os.makedirs(self.test_dir)

    def compile_test(self, test_file):
        try:
            result = subprocess.run(['nasm', '-f', 'elf32', test_file, '-o', f'{test_file}.o'],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return True, ''
            return False, result.stderr
        except Exception as e:
            return False, str(e)

    def run_test(self, test_file):
        try:
            # Link the object file
            obj_file = f'{test_file}.o'
            exe_file = f'{test_file}.exe'
            link_result = subprocess.run(['ld', '-m', 'elf_i386', obj_file, '-o', exe_file],
                                       capture_output=True, text=True)
            if link_result.returncode != 0:
                return False, link_result.stderr

            # Run the test
            run_result = subprocess.run([f'./{exe_file}'],
                                      capture_output=True, text=True)
            return True, run_result.stdout
        except Exception as e:
            return False, str(e)

    def validate_utf8(self, content):
        try:
            content.encode('utf-8').decode('utf-8')
            return True
        except UnicodeError:
            return False

    def run_all_tests(self):
        test_files = [f for f in os.listdir(self.test_dir) 
                     if f.endswith('.asm') or f.endswith('.snkt')]
        
        for test_file in test_files:
            full_path = os.path.join(self.test_dir, test_file)
            test_result = {
                'name': test_file,
                'timestamp': datetime.now().isoformat(),
                'status': 'failed',
                'error': None
            }

            # Validate UTF-8 encoding
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if not self.validate_utf8(content):
                    test_result['error'] = 'Invalid UTF-8 encoding'
                    self.test_results['tests'].append(test_result)
                    self.test_results['failed'] += 1
                    continue

            # Compile test
            success, error = self.compile_test(full_path)
            if not success:
                test_result['error'] = f'Compilation error: {error}'
                self.test_results['tests'].append(test_result)
                self.test_results['failed'] += 1
                continue

            # Run test
            success, output = self.run_test(full_path)
            if not success:
                test_result['error'] = f'Runtime error: {output}'
                self.test_results['tests'].append(test_result)
                self.test_results['failed'] += 1
                continue

            # Test passed
            test_result['status'] = 'passed'
            test_result['output'] = output
            self.test_results['tests'].append(test_result)
            self.test_results['passed'] += 1

            self.test_results['total'] += 1

    def generate_report(self):
        report = {
            'summary': {
                'total': self.test_results['total'],
                'passed': self.test_results['passed'],
                'failed': self.test_results['failed'],
                'pass_rate': f"{(self.test_results['passed'] / self.test_results['total'] * 100):.2f}%"
                if self.test_results['total'] > 0 else '0%'
            },
            'tests': self.test_results['tests']
        }

        # Save report to file
        with open('test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # Print summary to console
        print('\nTest Results Summary:')
        print(f"Total Tests: {report['summary']['total']}")
        print(f"Passed: {report['summary']['passed']}")
        print(f"Failed: {report['summary']['failed']}")
        print(f"Pass Rate: {report['summary']['pass_rate']}")

def main():
    runner = SanskritTestRunner()
    runner.run_all_tests()
    runner.generate_report()

if __name__ == '__main__':
    main()