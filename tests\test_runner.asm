; Test Runner for Sanskrit Language
section .data
    test_passed_msg db 'परीक्षण सफल: ', 0
    test_failed_msg db 'परीक्षण असफल: ', 0
    newline db 0xA
    summary_msg db 'कुल परीक्षण: ', 0
    passed_msg db 'सफल: ', 0
    failed_msg db 'असफल: ', 0

    ; Test cases
    test1_name db 'परिवर्तनीय घोषणा', 0
    test1_input db 'परिवर्तनीय x = १०', 0
    test1_expected db '१०', 0

    test2_name db 'मुद्रण कार्य', 0
    test2_input db 'लेखय(x)', 0
    test2_expected db '१०', 0

    ; Counters
    tests_total dq 0
    tests_passed dq 0
    tests_failed dq 0

section .bss
    test_buffer resb 1024
    output_buffer resb 1024

section .text
    global _start
    extern execute_sanskrit
    extern compare_output

_start:
    ; Initialize test environment
    call setup_tests

    ; Run test cases
    mov rdi, test1_name
    mov rsi, test1_input
    mov rdx, test1_expected
    call run_test

    mov rdi, test2_name
    mov rsi, test2_input
    mov rdx, test2_expected
    call run_test

    ; Print summary
    call print_summary

    ; Exit program
    mov rax, 60         ; sys_exit
    xor rdi, rdi        ; status = 0
    syscall

setup_tests:
    ; Initialize test environment
    xor rax, rax
    mov [tests_total], rax
    mov [tests_passed], rax
    mov [tests_failed], rax
    ret

run_test:
    push rbp
    mov rbp, rsp

    ; Increment total tests
    inc qword [tests_total]

    ; Execute test
    push rdi            ; Save test name
    push rdx            ; Save expected output

    ; Run Sanskrit code
    mov rdi, rsi        ; Input code
    mov rsi, output_buffer
    call execute_sanskrit

    ; Compare output
    mov rdi, output_buffer
    pop rsi             ; Expected output
    call compare_output
    test rax, rax
    jz test_failed

test_passed:
    inc qword [tests_passed]
    pop rdi             ; Restore test name
    call print_test_passed
    jmp test_done

test_failed:
    inc qword [tests_failed]
    pop rdi             ; Restore test name
    call print_test_failed

test_done:
    mov rsp, rbp
    pop rbp
    ret

print_test_passed:
    push rdi
    mov rax, 1          ; sys_write
    mov rdi, 1          ; stdout
    mov rsi, test_passed_msg
    mov rdx, 14         ; length
    syscall
    pop rsi             ; Test name
    call print_string
    call print_newline
    ret

print_test_failed:
    push rdi
    mov rax, 1          ; sys_write
    mov rdi, 1          ; stdout
    mov rsi, test_failed_msg
    mov rdx, 14         ; length
    syscall
    pop rsi             ; Test name
    call print_string
    call print_newline
    ret

print_summary:
    ; Print total tests
    mov rax, 1
    mov rdi, 1
    mov rsi, summary_msg
    mov rdx, 13
    syscall
    mov rax, [tests_total]
    call print_number
    call print_newline

    ; Print passed tests
    mov rax, 1
    mov rdi, 1
    mov rsi, passed_msg
    mov rdx, 7
    syscall
    mov rax, [tests_passed]
    call print_number
    call print_newline

    ; Print failed tests
    mov rax, 1
    mov rdi, 1
    mov rsi, failed_msg
    mov rdx, 7
    syscall
    mov rax, [tests_failed]
    call print_number
    call print_newline
    ret

print_string:
    push rbp
    mov rbp, rsp
    push rdi
    push rsi
    ; Calculate string length
    mov rdi, rsi
    xor rcx, rcx
str_len:
    cmp byte [rdi], 0
    je str_print
    inc rcx
    inc rdi
    jmp str_len
str_print:
    mov rax, 1          ; sys_write
    mov rdi, 1          ; stdout
    ; rsi already contains string pointer
    mov rdx, rcx        ; length
    syscall
    pop rsi
    pop rdi
    mov rsp, rbp
    pop rbp
    ret

print_number:
    push rbp
    mov rbp, rsp
    sub rsp, 32         ; Local buffer for digits
    mov rdi, rsp
    add rdi, 31         ; Start from end of buffer
    mov byte [rdi], 0   ; Null terminate

convert_loop:
    dec rdi
    mov rdx, 0
    mov rcx, 10
    div rcx
    add dl, '०'         ; Convert to Devanagari numeral
    mov [rdi], dl
    test rax, rax
    jnz convert_loop

    mov rsi, rdi
    call print_string
    mov rsp, rbp
    pop rbp
    ret

print_newline:
    mov rax, 1
    mov rdi, 1
    mov rsi, newline
    mov rdx, 1
    syscall
    ret