section .data
    ; Error types in Sanskrit and Hindi
    err_type_syntax db 'वाक्यरचना त्रुटि', 0
    err_type_runtime db 'कार्यकाल त्रुटि', 0
    err_type_memory db 'स्मृति त्रुटि', 0
    err_type_stack db 'स्टैक त्रुटि', 0
    err_type_type db 'प्रकार त्रुटि', 0
    err_type_io db 'इनपुट/आउटपुट त्रुटि', 0

    ; Error messages in Hindi
    err_msg_hi_syntax db 'वाक्य रचना में गलती', 0
    err_msg_hi_runtime db 'प्रोग्राम चलने में त्रुटि', 0
    err_msg_hi_memory db 'मेमोरी में समस्या', 0
    err_msg_hi_stack db 'स्टैक में समस्या', 0
    err_msg_hi_type db 'डेटा प्रकार में गलती', 0
    err_msg_hi_io db 'इनपुट/आउटपुट में समस्या', 0

    ; Try-catch related messages
    try_block_start db 'प्रयास_प्रारंभ', 0
    try_block_end db 'प्रयास_समाप्त', 0
    catch_block_start db 'त्रुटि_नियंत्रण', 0
    finally_block db 'अंतिम_कार्य', 0

    ; Stack trace format
    stack_trace_msg db 'स्टैक अनुरेखण:', 0xA, 0
    stack_frame_format db '  %s में %d पंक्ति पर', 0xA, 0

    ; Error recovery messages
    recovery_attempt db 'त्रुटि से उबरने का प्रयास...', 0xA, 0
    recovery_success db 'त्रुटि से सफलतापूर्वक उबर गए', 0xA, 0
    recovery_failed db 'त्रुटि से उबरने में असफल', 0xA, 0

section .bss
    ; Error handling state
    current_error_type resd 1
    error_line_number resd 1
    error_char_position resd 1
    error_recovery_mode resb 1

    ; Try-catch state
    try_catch_stack resb 1024
    try_catch_sp resd 1

    ; Stack trace buffer
    stack_trace_buffer resb 4096
    stack_trace_pos resd 1

section .text
    global error_handler_init
    global push_try_block
    global pop_try_block
    global raise_error
    global begin_error_recovery
    global end_error_recovery
    global get_stack_trace

; Initialize error handler
error_handler_init:
    push ebp
    mov ebp, esp
    
    ; Initialize error handling state
    mov dword [current_error_type], 0
    mov dword [error_line_number], 1
    mov dword [error_char_position], 1
    mov byte [error_recovery_mode], 0
    
    ; Initialize try-catch stack
    mov dword [try_catch_sp], 0
    
    ; Clear stack trace buffer
    mov edi, stack_trace_buffer
    mov ecx, 4096
    xor eax, eax
    rep stosb
    
    mov esp, ebp
    pop ebp
    ret

; Push try block
push_try_block:
    push ebp
    mov ebp, esp
    
    ; Get current stack pointer
    mov eax, [try_catch_sp]
    
    ; Store current state
    mov ebx, [ebp + 8]  ; Try block address
    mov [try_catch_stack + eax], ebx
    
    ; Update stack pointer
    add dword [try_catch_sp], 4
    
    mov esp, ebp
    pop ebp
    ret

; Raise an error
raise_error:
    push ebp
    mov ebp, esp
    
    ; Parameters:
    ; [ebp + 8] = error type
    ; [ebp + 12] = error message
    ; [ebp + 16] = line number
    ; [ebp + 20] = char position
    
    ; Store error information
    mov eax, [ebp + 8]
    mov [current_error_type], eax
    mov eax, [ebp + 16]
    mov [error_line_number], eax
    mov eax, [ebp + 20]
    mov [error_char_position], eax
    
    ; Check if in try block
    mov eax, [try_catch_sp]
    test eax, eax
    jz .no_try_block
    
    ; Jump to catch handler
    sub eax, 4
    mov ebx, [try_catch_stack + eax]
    jmp ebx
    
.no_try_block:
    ; Print error message and stack trace
    push dword [ebp + 12]
    call print_error_message
    add esp, 4
    
    call get_stack_trace
    
    mov esp, ebp
    pop ebp
    ret

; Begin error recovery
begin_error_recovery:
    mov byte [error_recovery_mode], 1
    ret

; End error recovery
end_error_recovery:
    mov byte [error_recovery_mode], 0
    ret

; Get stack trace
get_stack_trace:
    push ebp
    mov ebp, esp
    
    ; Print stack trace header
    mov eax, 4
    mov ebx, 1
    mov ecx, stack_trace_msg
    mov edx, 16
    int 0x80
    
    ; Format and print stack frames
    mov esi, [stack_trace_buffer]
    mov edi, [stack_trace_pos]
    
.print_frame:
    cmp esi, edi
    jge .done
    
    push dword [esi + 4]  ; Line number
    push dword [esi]      ; Function name
    push stack_frame_format
    call printf
    add esp, 12
    
    add esi, 8
    jmp .print_frame
    
.done:
    mov esp, ebp
    pop ebp
    ret