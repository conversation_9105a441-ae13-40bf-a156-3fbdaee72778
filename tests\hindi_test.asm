section .data
    test_var db 'Testing Hindi variable (चर)...', 0xA
    test_var_len equ $ - test_var
    test_print db 'Testing Hindi print (लिखो)...', 0xA
    test_print_len equ $ - test_print
    test_if db 'Testing Hindi if/else (अगर/वरना)...', 0xA
    test_if_len equ $ - test_if
    test_loop db 'Testing Hindi loop (दोहराओ)...', 0xA
    test_loop_len equ $ - test_loop
    test_func db 'Testing Hindi function (कार्य)...', 0xA
    test_func_len equ $ - test_func
    
    test_input1 db 'चर क = १०', 0
    test_input2 db 'लिखो(क)', 0
    test_input3 db 'अगर क > ५ लिखो("बड़ा")', 0
    test_input4 db 'दोहराओ ३ लिखो("पुनः")', 0

section .text
    global _start
    extern init_hindi_keywords
    extern parse_command

_start:
    ; Initialize Hindi support
    call init_hindi_keywords

    ; Test variable declaration
    mov eax, 4
    mov ebx, 1
    mov ecx, test_var
    mov edx, test_var_len
    int 0x80

    mov esi, test_input1
    call parse_command

    ; Test print statement
    mov eax, 4
    mov ebx, 1
    mov ecx, test_print
    mov edx, test_print_len
    int 0x80

    mov esi, test_input2
    call parse_command

    ; Test conditional
    mov eax, 4
    mov ebx, 1
    mov ecx, test_if
    mov edx, test_if_len
    int 0x80

    mov esi, test_input3
    call parse_command

    ; Test loop
    mov eax, 4
    mov ebx, 1
    mov ecx, test_loop
    mov edx, test_loop_len
    int 0x80

    mov esi, test_input4
    call parse_command

    ; Exit
    mov eax, 1
    xor ebx, ebx
    int 0x80