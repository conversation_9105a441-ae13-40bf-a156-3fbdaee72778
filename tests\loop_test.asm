; Test file for loop structures in Sanskrit
section .data
    test_header db 'Running Loop Tests', 0xA
    test_header_len equ $ - test_header
    test_pass db 'Test Passed: ', 0
    test_pass_len equ $ - test_pass
    test_fail db 'Test Failed: ', 0
    test_fail_len equ $ - test_fail
    newline db 0xA

    ; Test inputs
    test_loop db 'परिवर्तनीय गणक = १', 0
    test_loop_cond db 'पुनरावर्तनम् गणक <= ५:', 0
    test_loop_incr db 'गणक = गणक + १', 0

    ; Expected results
    expected_final_count equ 6

section .text
    global _start

_start:
    ; Print test header
    mov eax, 4
    mov ebx, 1
    mov ecx, test_header
    mov edx, test_header_len
    int 0x80

    ; Initialize counter
    mov esi, test_loop
    call parse_command

    ; Test loop execution
    mov ecx, 5          ; Loop count
loop_test:
    push ecx
    mov esi, test_loop_cond
    call parse_command
    mov esi, test_loop_incr
    call parse_command
    pop ecx
    loop loop_test

    ; Verify final counter value
    mov edi, var_table
    mov eax, [edi + 4]
    cmp eax, expected_final_count
    jne test_fail

    ; Test passed
    mov eax, 4
    mov ebx, 1
    mov ecx, test_pass
    mov edx, test_pass_len
    int 0x80
    jmp exit_program

test_fail:
    mov eax, 4
    mov ebx, 1
    mov ecx, test_fail
    mov edx, test_fail_len
    int 0x80

exit_program:
    mov eax, 1
    xor ebx, ebx
    int 0x80