{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "Sanskrit", "patterns": [{"include": "#keywords"}, {"include": "#strings"}, {"include": "#numbers"}, {"include": "#comments"}], "repository": {"keywords": {"patterns": [{"name": "keyword.control.sanskrit", "match": "\\b(यदि|अन्यथा|पुनरावर्तनम्|कार्य|लेखय|परिवर्तनीय)\\b"}, {"name": "keyword.control.hindi", "match": "\\b(अगर|वरना|दोहराओ|कार्य|लिखो|चर)\\b"}]}, "strings": {"name": "string.quoted.double.sanskrit", "begin": "\"", "end": "\"", "patterns": [{"name": "constant.character.escape.sanskrit", "match": "\\\\."}]}, "numbers": {"name": "constant.numeric.sanskrit", "match": "\\b([०-९]+)\\b"}, "comments": {"name": "comment.line.double-slash.sanskrit", "match": "//.*$"}}, "scopeName": "source.sanskrit"}