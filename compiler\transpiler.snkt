टिप्पणी संस्कृत ट्रांसपाइलर मॉड्यूल

टिप्पणी एएसटी नोड प्रकार
परिवर्तनीय नोड_कार्यक्रम = "कार्यक्रम"
परिवर्तनीय नोड_घोषणा = "घोषणा"
परिवर्तनीय नोड_कार्य = "कार्य"
परिवर्तनीय नोड_कथन = "कथन"
परिवर्तनीय नोड_अभिव्यक्ति = "अभिव्यक्ति"

टिप्पणी लक्ष्य भाषा प्रकार
परिवर्तनीय लक्ष्य_जावास्क्रिप्ट = "javascript"
परिवर्तनीय लक्ष्य_पाइथन = "python"
परिवर्तनीय लक्ष्य_सी = "c"

टिप्पणी एएसटी निर्माण कार्य
कार्य एएसटी_बनाना(टोकन_सूची) {
    परिवर्तनीय एएसटी = {प्रकार: नोड_कार्यक्रम, बच्चे: सूचि()}
    
    पुनरावर्तनम् (टोकन_सूची.लंबाई() > ०) {
        परिवर्तनीय टोकन = टोकन_सूची.हटाओ()
        
        यदि (टोकन.प्रकार == टोकन_कुंजीशब्द) {
            यदि (टोकन.मान == "कार्य") {
                एएसटी.बच्चे.जोड़ें(कार्य_घोषणा_पार्स(टोकन_सूची))
            } अन्यथा यदि (टोकन.मान == "परिवर्तनीय") {
                एएसटी.बच्चे.जोड़ें(चर_घोषणा_पार्स(टोकन_सूची))
            }
        }
    }
    
    एएसटी
}

टिप्पणी कोड जनरेशन कार्य
कार्य कोड_जनरेट(एएसटी, लक्ष्य_भाषा) {
    यदि (लक्ष्य_भाषा == लक्ष्य_जावास्क्रिप्ट) {
        जावास्क्रिप्ट_जनरेट(एएसटी)
    } अन्यथा यदि (लक्ष्य_भाषा == लक्ष्य_पाइथन) {
        पाइथन_जनरेट(एएसटी)
    } अन्यथा यदि (लक्ष्य_भाषा == लक्ष्य_सी) {
        सी_जनरेट(एएसटी)
    }
}

टिप्पणी जावास्क्रिप्ट कोड जनरेशन
कार्य जावास्क्रिप्ट_जनरेट(नोड) {
    यदि (नोड.प्रकार == नोड_कार्यक्रम) {
        परिवर्तनीय कोड = ""
        नोड.बच्चे.प्रत्येक(बच्चा => {
            कोड += जावास्क्रिप्ट_जनरेट(बच्चा) + "\n"
        })
        कोड
    } अन्यथा यदि (नोड.प्रकार == नोड_कार्य) {
        "function " + नोड.नाम + "(" + नोड.पैरामीटर.जोड़ें(",") + ") {\n" +
        नोड.शरीर.मैप(कथन => जावास्क्रिप्ट_जनरेट(कथन)).जोड़ें("\n") +
        "\n}"
    }
}

टिप्पणी पाइथन कोड जनरेशन
कार्य पाइथन_जनरेट(नोड) {
    यदि (नोड.प्रकार == नोड_कार्यक्रम) {
        परिवर्तनीय कोड = ""
        नोड.बच्चे.प्रत्येक(बच्चा => {
            कोड += पाइथन_जनरेट(बच्चा) + "\n"
        })
        कोड
    } अन्यथा यदि (नोड.प्रकार == नोड_कार्य) {
        "def " + नोड.नाम + "(" + नोड.पैरामीटर.जोड़ें(",") + "):" +
        नोड.शरीर.मैप(कथन => "    " + पाइथन_जनरेट(कथन)).जोड़ें("\n")
    }
}

टिप्पणी सी कोड जनरेशन
कार्य सी_जनरेट(नोड) {
    यदि (नोड.प्रकार == नोड_कार्यक्रम) {
        "#include <stdio.h>\n\n" +
        नोड.बच्चे.मैप(बच्चा => सी_जनरेट(बच्चा)).जोड़ें("\n")
    } अन्यथा यदि (नोड.प्रकार == नोड_कार्य) {
        नोड.लौटाना_प्रकार + " " + नोड.नाम + "(" + नोड.पैरामीटर.जोड़ें(",") + ") {\n" +
        नोड.शरीर.मैप(कथन => "    " + सी_जनरेट(कथन)).जोड़ें("\n") +
        "\n}"
    }
}