section .data
    ; Command strings
    cmd_install db 'install', 0
    
    ; Messages
    msg_installing db 'Installing package: ', 0
    msg_installing_len equ $ - msg_installing
    msg_success db 'Package installed successfully', 0xA
    msg_success_len equ $ - msg_success
    msg_error db 'Error: Package installation failed', 0xA
    msg_error_len equ $ - msg_error
    msg_usage db 'Usage: snkt install <package_name>', 0xA
    msg_usage_len equ $ - msg_usage
    
    ; Package repository URL (can be configured)
    repo_url db 'https://sanskrit-packages.org/packages/', 0
    
    ; Buffer sizes
    buffer_size equ 1024
    path_max equ 260

section .bss
    package_name resb buffer_size
    download_path resb path_max
    install_path resb path_max
    command_buffer resb buffer_size

section .text
    global _start

_start:
    ; Get command line arguments
    pop ecx         ; Get argc
    cmp ecx, 3     ; Check if we have correct number of arguments
    jl usage_error
    
    pop eax         ; Skip program name
    pop esi         ; Get command (should be 'install')
    pop edi         ; Get package name
    
    ; Verify command is 'install'
    push edi        ; Save package name
    mov edi, cmd_install
    call str_compare
    pop edi         ; Restore package name
    test eax, eax
    jz usage_error
    
    ; Copy package name to buffer
    mov esi, edi
    mov edi, package_name
    call str_copy
    
    ; Display installing message
    mov eax, 4
    mov ebx, 1
    mov ecx, msg_installing
    mov edx, msg_installing_len
    int 0x80
    
    mov eax, 4
    mov ebx, 1
    mov ecx, package_name
    call str_len
    mov edx, eax
    int 0x80
    
    ; Create package directory if it doesn't exist
    call create_package_dir
    test eax, eax
    jz installation_error
    
    ; Download package
    call download_package
    test eax, eax
    jz installation_error
    
    ; Install package
    call install_package
    test eax, eax
    jz installation_error
    
    ; Success message
    mov eax, 4
    mov ebx, 1
    mov ecx, msg_success
    mov edx, msg_success_len
    int 0x80
    
    jmp exit_success

usage_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, msg_usage
    mov edx, msg_usage_len
    int 0x80
    jmp exit_error

installation_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, msg_error
    mov edx, msg_error_len
    int 0x80
    jmp exit_error

; String comparison function
str_compare:
    push ebx
compare_loop:
    mov bl, [esi]
    mov bh, [edi]
    cmp bl, bh
    jne compare_end
    test bl, bl
    jz compare_match
    inc esi
    inc edi
    jmp compare_loop
compare_match:
    mov eax, 1
    jmp compare_done
compare_end:
    xor eax, eax
compare_done:
    pop ebx
    ret

; String copy function
str_copy:
    push ebx
copy_loop:
    mov bl, [esi]
    mov [edi], bl
    test bl, bl
    jz copy_done
    inc esi
    inc edi
    jmp copy_loop
copy_done:
    pop ebx
    ret

; String length function
str_len:
    push edi
    mov edi, ecx
    xor eax, eax
len_loop:
    cmp byte [edi], 0
    je len_done
    inc eax
    inc edi
    jmp len_loop
len_done:
    pop edi
    ret

; Create package directory
create_package_dir:
    ; Create lib directory if it doesn't exist
    mov eax, 0x27         ; sys_mkdir
    mov ebx, 'lib'        ; directory name
    mov ecx, 0o755        ; permissions
    int 0x80
    
    ; Prepare package directory path
    mov esi, package_name
    mov edi, install_path
    mov eax, 'lib/'
    stosd
    call str_copy
    
    ; Create package directory
    mov eax, 0x27         ; sys_mkdir
    mov ebx, install_path
    mov ecx, 0o755        ; permissions
    int 0x80
    
    test eax, eax
    js dir_error          ; Jump if error (negative return value)
    mov eax, 1            ; Return success
    ret

dir_error:
    xor eax, eax          ; Return failure
    ret

; Download package
download_package:
    ; Prepare download path
    mov esi, repo_url
    mov edi, download_path
    call str_copy
    
    mov esi, package_name
    call str_copy
    
    ; Here we would make a network request to download the package
    ; For now, we'll simulate a successful download
    ; In a real implementation, this would use proper networking syscalls
    
    ; Check if download was successful
    ; For now, we'll assume success if the package name is valid
    mov esi, package_name
    call str_len
    test eax, eax
    jz download_error
    
    mov eax, 1            ; Return success
    ret

download_error:
    xor eax, eax          ; Return failure
    ret

; Install package
install_package:
    ; Prepare installation path
    mov esi, package_name
    mov edi, install_path
    mov eax, 'lib/'
    stosd
    call str_copy
    
    ; Here we would:
    ; 1. Extract the downloaded package
    ; 2. Copy files to installation directory
    ; 3. Process any dependencies
    ; 4. Update package registry
    
    ; For now, we'll simulate successful installation
    ; In a real implementation, this would handle file operations
    
    ; Verify installation path exists
    mov eax, 5          ; sys_open
    mov ebx, install_path
    xor ecx, ecx        ; O_RDONLY
    int 0x80
    
    test eax, eax
    js install_error
    
    mov eax, 1          ; Return success
    ret

install_error:
    xor eax, eax        ; Return failure
    ret

exit_success:
    mov eax, 1      ; sys_exit
    xor ebx, ebx    ; return 0
    int 0x80

exit_error:
    mov eax, 1      ; sys_exit
    mov ebx, 1      ; return 1
    int 0x80