section .data
    ; File operation keywords and messages
    kw_open db 'खोल', 0         ; Open file
    kw_close db 'बंद', 0        ; Close file
    kw_read db 'पठन', 0         ; Read file
    kw_write db 'लेखन', 0       ; Write file
    kw_append db 'जोड़', 0       ; Append to file

    ; Error messages
    error_open db 'त्रुटि: Cannot open file', 0xA
    error_open_len equ $ - error_open
    error_read db 'त्रुटि: Cannot read file', 0xA
    error_read_len equ $ - error_read
    error_write db 'त्रुटि: Cannot write file', 0xA
    error_write_len equ $ - error_write

    ; File modes
    O_RDONLY equ 0
    O_WRONLY equ 1
    O_RDWR equ 2
    O_CREAT equ 64
    O_APPEND equ 1024

    ; File permissions
    mode_rw equ 0644o           ; rw-r--r--

section .bss
    ; File descriptors table
    max_files equ 16
    file_descriptors resb max_files * 4
    file_count dd 0

    ; I/<PERSON> buffer
    buffer_size equ 4096
    io_buffer resb buffer_size

section .text
    global file_open
    global file_close
    global file_read
    global file_write
    global file_append

; Open file
; Parameters: filename (ebp+8), mode (ebp+12)
; Returns: file descriptor (or -1 on error)
file_open:
    push ebp
    mov ebp, esp
    push ebx

    ; Check if we can open more files
    mov eax, [file_count]
    cmp eax, max_files
    jge open_error

    ; Open file
    mov eax, 5          ; sys_open
    mov ebx, [ebp + 8]  ; filename
    mov ecx, [ebp + 12] ; mode
    mov edx, mode_rw    ; permissions
    int 0x80

    ; Check for error
    test eax, eax
    js open_error

    ; Store file descriptor
    mov ebx, [file_count]
    mov [file_descriptors + ebx * 4], eax
    inc dword [file_count]

    pop ebx
    mov esp, ebp
    pop ebp
    ret 8

; Close file
; Parameter: fd (ebp+8)
file_close:
    push ebp
    mov ebp, esp

    mov eax, 6          ; sys_close
    mov ebx, [ebp + 8]  ; file descriptor
    int 0x80

    mov esp, ebp
    pop ebp
    ret 4

; Read from file
; Parameters: fd (ebp+8), buffer (ebp+12), count (ebp+16)
; Returns: number of bytes read (or -1 on error)
file_read:
    push ebp
    mov ebp, esp

    mov eax, 3          ; sys_read
    mov ebx, [ebp + 8]  ; file descriptor
    mov ecx, [ebp + 12] ; buffer
    mov edx, [ebp + 16] ; count
    int 0x80

    mov esp, ebp
    pop ebp
    ret 12

; Write to file
; Parameters: fd (ebp+8), buffer (ebp+12), count (ebp+16)
; Returns: number of bytes written (or -1 on error)
file_write:
    push ebp
    mov ebp, esp

    mov eax, 4          ; sys_write
    mov ebx, [ebp + 8]  ; file descriptor
    mov ecx, [ebp + 12] ; buffer
    mov edx, [ebp + 16] ; count
    int 0x80

    mov esp, ebp
    pop ebp
    ret 12

; Append to file (combination of open and write)
; Parameters: filename (ebp+8), buffer (ebp+12), count (ebp+16)
; Returns: number of bytes written (or -1 on error)
file_append:
    push ebp
    mov ebp, esp
    push ebx

    ; Open file in append mode
    push dword O_WRONLY | O_APPEND | O_CREAT
    push dword [ebp + 8]    ; filename
    call file_open
    add esp, 8

    ; Check for error
    test eax, eax
    js append_error

    ; Write data
    push dword [ebp + 16]   ; count
    push dword [ebp + 12]   ; buffer
    push eax                ; file descriptor
    call file_write
    add esp, 12

    ; Close file
    push eax                ; Save return value
    push dword [file_descriptors + eax * 4]
    call file_close
    pop eax                 ; Restore return value

    pop ebx
    mov esp, ebp
    pop ebp
    ret 12

; Error handlers
open_error:
append_error:
    mov eax, -1
    ret