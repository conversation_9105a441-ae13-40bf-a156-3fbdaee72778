# Sanskrit Programming Language Mobile App

A mobile application that allows users to write and execute Sanskrit programming language code on their phones.

## Features

- Cross-platform mobile interface (iOS and Android)
- Code editor with Sanskrit syntax highlighting
- Offline code storage
- Touch-friendly UI elements
- Code execution capabilities

## Setup

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm start
```

3. Run on your device:
- For iOS: `npm run ios`
- For Android: `npm run android`

## Usage

1. Open the app
2. Write Sanskrit code in the editor
3. Use the "Run" button to execute the code
4. View output in the output panel
5. Save your code using the "Save" button

## Development

Built with:
- React Native
- Expo
- react-native-code-editor
- AsyncStorage for offline storage

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.