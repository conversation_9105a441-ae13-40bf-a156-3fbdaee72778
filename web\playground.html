<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sanskrit Programming Playground</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .editor-container {
            display: flex;
            gap: 20px;
        }
        .editor-section {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .output-section {
            flex: 1;
            background: #2d2d2d;
            color: #fff;
            border-radius: 8px;
            padding: 15px;
            min-height: 300px;
        }
        .toolbar {
            margin-bottom: 10px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #45a049;
        }
        .CodeMirror {
            height: 400px;
            font-size: 14px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>संस्कृत प्रोग्रामिंग प्लेग्राउंड</h1>
            <p>Sanskrit & Hindi Programming Language Playground</p>
        </div>
        
        <div class="editor-container">
            <div class="editor-section">
                <div class="toolbar">
                    <button onclick="runCode()">चलाएं (Run)</button>
                    <button onclick="clearEditor()">साफ़ करें (Clear)</button>
                </div>
                <textarea id="editor">// Sanskrit example
लेखय "नमस्ते संसार";

// Hindi example
लिखो "नमस्ते दुनिया";</textarea>
            </div>
            <div class="output-section">
                <h3>आउटपुट (Output)</h3>
                <pre id="output"></pre>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script>
        // Initialize CodeMirror
        const editor = CodeMirror.fromTextArea(document.getElementById('editor'), {
            mode: 'javascript',
            theme: 'monokai',
            lineNumbers: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            indentUnit: 4,
            tabSize: 4,
            lineWrapping: true
        });

        // Define Sanskrit and Hindi keywords for syntax highlighting
        CodeMirror.defineMode("sanskrit", function(config) {
            return {
                token: function(stream, state) {
                    if (stream.match(/लेखय|परिवर्तनीय|यदि|अन्यथा|पुनरावर्तनम्|कार्य/)) {
                        return "keyword";
                    }
                    if (stream.match(/लिखो|चर|अगर|वरना|दोहराओ|कार्य/)) {
                        return "keyword";
                    }
                    if (stream.match(/[०-९]+/)) {
                        return "number";
                    }
                    if (stream.match(/\/\//)) {
                        stream.skipToEnd();
                        return "comment";
                    }
                    stream.next();
                    return null;
                }
            };
        });

        editor.setOption("mode", "sanskrit");

        function runCode() {
            const code = editor.getValue();
            const output = document.getElementById('output');
            try {
                // Here we would integrate with the Sanskrit interpreter
                output.textContent = 'Code execution simulation:\n' + code;
            } catch (error) {
                output.textContent = 'Error: ' + error.message;
            }
        }

        function clearEditor() {
            editor.setValue('');
            document.getElementById('output').textContent = '';
        }
    </script>
</body>
</html>