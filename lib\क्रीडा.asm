section .data
    ; Game engine keywords and messages
    kw_init db 'आरम्भ', 0          ; Initialize game
    kw_update db 'अद्यतन', 0       ; Update game state
    kw_render db 'चित्रण', 0        ; Render frame
    kw_sprite db 'चित्रक', 0        ; Sprite object
    kw_input db 'निवेश', 0          ; Input handling
    kw_collision db 'संघर्ष', 0      ; Collision detection

    ; Input keys
    key_up db 'ऊपर', 0
    key_down db 'नीचे', 0
    key_left db 'बायाँ', 0
    key_right db 'दायाँ', 0
    key_space db 'अंतर', 0

    ; Error messages
    error_init db 'त्रुटि: Cannot initialize game engine', 0xA
    error_init_len equ $ - error_init
    error_render db 'त्रुटि: Rendering error', 0xA
    error_render_len equ $ - error_render

    ; Game configuration
    max_sprites equ 256
    max_colliders equ 256
    screen_width dd 800
    screen_height dd 600

section .bss
    ; Sprite management
    sprite_x resd max_sprites      ; X positions
    sprite_y resd max_sprites      ; Y positions
    sprite_w resd max_sprites      ; Widths
    sprite_h resd max_sprites      ; Heights
    sprite_active resb max_sprites ; Active flags
    sprite_count resd 1           ; Number of active sprites

    ; Collision system
    collider_x resd max_colliders
    collider_y resd max_colliders
    collider_w resd max_colliders
    collider_h resd max_colliders
    collider_active resb max_colliders
    collider_count resd 1

    ; Input state
    key_states resb 256           ; Keyboard state buffer

section .text
    global game_init
    global game_update
    global game_render
    global handle_input
    global check_collision

; Initialize game engine
game_init:
    push ebp
    mov ebp, esp

    ; Initialize sprite system
    mov dword [sprite_count], 0
    mov dword [collider_count], 0

    ; Clear input state
    mov ecx, 256
    mov edi, key_states
    xor eax, eax
    rep stosb

    mov esp, ebp
    pop ebp
    ret

; Update game state
game_update:
    push ebp
    mov ebp, esp

    ; Update sprite positions
    mov ecx, [sprite_count]
    test ecx, ecx
    jz .update_done

    ; Update collision state
    call check_collision

.update_done:
    mov esp, ebp
    pop ebp
    ret

; Render game frame
game_render:
    push ebp
    mov ebp, esp

    ; Render active sprites
    mov ecx, [sprite_count]
    test ecx, ecx
    jz .render_done

.render_sprite:
    ; Implement sprite rendering here
    loop .render_sprite

.render_done:
    mov esp, ebp
    pop ebp
    ret

; Handle input
handle_input:
    push ebp
    mov ebp, esp

    ; Read keyboard state
    mov eax, 0      ; sys_read
    mov ebx, 0      ; stdin
    mov ecx, key_states
    mov edx, 1
    int 0x80

    mov esp, ebp
    pop ebp
    ret

; Check collision between sprites
check_collision:
    push ebp
    mov ebp, esp

    mov ecx, [sprite_count]
    dec ecx         ; n-1 iterations for pairs
    jz .collision_done

.check_pair:
    push ecx

    ; Compare current sprite with all others
    mov edx, ecx
.compare_sprites:
    ; Implement AABB collision detection here
    dec edx
    jnz .compare_sprites

    pop ecx
    loop .check_pair

.collision_done:
    mov esp, ebp
    pop ebp
    ret