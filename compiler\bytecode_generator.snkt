टिप्पणी संस्कृत बाइटकोड जनरेटर और वर्चुअल मशीन

टिप्पणी बाइटकोड ऑपकोड परिभाषाएं
परिवर्तनीय ऑप_जोड़ = 0x01
परिवर्तनीय ऑप_घटा = 0x02
परिवर्तनीय ऑप_गुणा = 0x03
परिवर्तनीय ऑप_भाग = 0x04
परिवर्तनीय ऑप_लोड = 0x05
परिवर्तनीय ऑप_स्टोर = 0x06
परिवर्तनीय ऑप_कूद = 0x07
परिवर्तनीय ऑप_कूद_यदि = 0x08
परिवर्तनीय ऑप_कार्य_बुलाओ = 0x09
परिवर्तनीय ऑप_वापसी = 0x0A

टिप्पणी बाइटकोड जनरेशन कार्य
कार्य बाइटकोड_जनरेट(एएसटी) {
    परिवर्तनीय बाइटकोड = सूचि()
    परिवर्तनीय लेबल_मैप = {}
    परिवर्तनीय वर्तमान_ऑफसेट = 0
    
    टिप्पणी एएसटी से बाइटकोड जनरेट करें
    एएसटी.बच्चे.प्रत्येक(बच्चा => {
        बाइटकोड.विस्तार(नोड_से_बाइटकोड(बच्चा, लेबल_मैप, वर्तमान_ऑफसेट))
    })
    
    बाइटकोड
}

टिप्पणी नोड से बाइटकोड जनरेशन
कार्य नोड_से_बाइटकोड(नोड, लेबल_मैप, ऑफसेट) {
    यदि (नोड.प्रकार == नोड_कार्य) {
        परिवर्तनीय कार्य_बाइटकोड = सूचि()
        
        टिप्पणी कार्य लेबल जोड़ें
        लेबल_मैप[नोड.नाम] = ऑफसेट
        
        टिप्पणी स्थानीय चर आवंटन
        नोड.स्थानीय_चर.प्रत्येक(चर => {
            कार्य_बाइटकोड.जोड़ें(ऑप_स्टोर)
            कार्य_बाइटकोड.जोड़ें(चर.स्लॉट)
        })
        
        टिप्पणी कार्य बॉडी जनरेट करें
        नोड.शरीर.प्रत्येक(कथन => {
            कार्य_बाइटकोड.विस्तार(कथन_से_बाइटकोड(कथन))
        })
        
        कार्य_बाइटकोड
    }
}

टिप्पणी कथन से बाइटकोड जनरेशन
कार्य कथन_से_बाइटकोड(कथन) {
    यदि (कथन.प्रकार == "आरोपण") {
        सूचि([
            ऑप_स्टोर,
            कथन.लक्ष्य.स्लॉट,
            ...अभिव्यक्ति_से_बाइटकोड(कथन.मान)
        ])
    } अन्यथा यदि (कथन.प्रकार == "वापसी") {
        सूचि([
            ऑप_वापसी,
            ...अभिव्यक्ति_से_बाइटकोड(कथन.मान)
        ])
    }
}

टिप्पणी अभिव्यक्ति से बाइटकोड जनरेशन
कार्य अभिव्यक्ति_से_बाइटकोड(अभिव्यक्ति) {
    यदि (अभिव्यक्ति.प्रकार == "संख्या") {
        सूचि([ऑप_लोड, अभिव्यक्ति.मान])
    } अन्यथा यदि (अभिव्यक्ति.प्रकार == "द्विआधारी") {
        परिवर्तनीय ऑप = अभिव्यक्ति.संचालक == "+" ? ऑप_जोड़ :
                      अभिव्यक्ति.संचालक == "-" ? ऑप_घटा :
                      अभिव्यक्ति.संचालक == "*" ? ऑप_गुणा :
                      ऑप_भाग
        
        सूचि([
            ...अभिव्यक्ति_से_बाइटकोड(अभिव्यक्ति.बायां),
            ...अभिव्यक्ति_से_बाइटकोड(अभिव्यक्ति.दायां),
            ऑप
        ])
    }
}

टिप्पणी वर्चुअल मशीन कार्यान्वयन
कार्य वीएम_निष्पादन(बाइटकोड) {
    टिप्पणी जेआईटी कम्पाइलर का उपयोग करें
    परिवर्तनीय जेआईटी = नया जेआईटी_कम्पाइलर()
    परिवर्तनीय अनुकूलित_कोड = जेआईटी.कम्पाइल(बाइटकोड)
    
    टिप्पणी स्मृति प्रबंधन अनुकूलन
    स्मृति_अनुकूलन()
    
    परिवर्तनीय स्टैक = सूचि()
    परिवर्तनीय स्थानीय_चर = {}
    परिवर्तनीय पीसी = 0
    
    पुनरावर्तनम् (पीसी < बाइटकोड.लंबाई()) {
        परिवर्तनीय ऑपकोड = बाइटकोड[पीसी]
        
        यदि (ऑपकोड == ऑप_लोड) {
            स्टैक.धक्का(बाइटकोड[पीसी + 1])
            पीसी += 2
        } अन्यथा यदि (ऑपकोड == ऑप_स्टोर) {
            स्थानीय_चर[बाइटकोड[पीसी + 1]] = स्टैक.निकालो()
            पीसी += 2
        } अन्यथा यदि (ऑपकोड >= ऑप_जोड && ऑपकोड <= ऑप_भाग) {
            परिवर्तनीय दायां = स्टैक.निकालो()
            परिवर्तनीय बायां = स्टैक.निकालो()
            
            यदि (ऑपकोड == ऑप_जोड़) स्टैक.धक्का(बायां + दायां)
            अन्यथा यदि (ऑपकोड == ऑप_घटा) स्टैक.धक्का(बायां - दायां)
            अन्यथा यदि (ऑपकोड == ऑप_गुणा) स्टैक.धक्का(बायां * दायां)
            अन्यथा स्टैक.धक्का(बायां / दायां)
            
            पीसी += 1
        } अन्यथा यदि (ऑपकोड == ऑप_वापसी) {
            वापसी स्टैक.निकालो()
        }
    }
}