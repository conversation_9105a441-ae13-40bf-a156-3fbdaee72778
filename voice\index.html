<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sanskrit Voice Coding</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .voice-controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .editor {
            border: 1px solid #ccc;
            padding: 10px;
            min-height: 400px;
        }
        .suggestions {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        .recording {
            background: #ff4444;
            color: white;
        }
    </style>
</head>
<body>
    <h1>संस्कृत Voice Coding</h1>
    
    <div class="voice-controls">
        <button id="startBtn">Start Voice Recognition</button>
        <button id="stopBtn" disabled>Stop</button>
    </div>

    <div class="container">
        <div>
            <h2>Voice Input</h2>
            <div id="transcript" class="editor"></div>
        </div>
        <div>
            <h2>Generated Code</h2>
            <div id="code" class="editor"></div>
        </div>
    </div>

    <div class="suggestions" id="suggestions"></div>

    <script src="voice_recognition.js"></script>
    <script src="ai_code_generator.js"></script>
    <script>
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const transcriptDiv = document.getElementById('transcript');
        const codeDiv = document.getElementById('code');
        const suggestionsDiv = document.getElementById('suggestions');

        // Initialize components
        const voiceRecognition = Object.create(SanskritVoiceRecognition);
        const aiCodeGenerator = Object.create(SanskritAICodeGenerator);

        voiceRecognition.init();
        aiCodeGenerator.init();

        // Handle voice recognition events
        window.addEventListener('codeGenerated', async (event) => {
            const transcript = event.detail;
            transcriptDiv.textContent = transcript;

            // Generate code using AI
            const generatedCode = await aiCodeGenerator.processVoiceInput(transcript);
            codeDiv.textContent = generatedCode;

            // Get code suggestions
            const suggestions = aiCodeGenerator.suggestCompletions(generatedCode);
            suggestionsDiv.innerHTML = suggestions
                .map(s => `<button onclick="applySuggestion('${s}')">${s}</button>`)
                .join('');
        });

        // Button event handlers
        startBtn.addEventListener('click', () => {
            voiceRecognition.start();
            startBtn.disabled = true;
            stopBtn.disabled = false;
            startBtn.classList.add('recording');
        });

        stopBtn.addEventListener('click', () => {
            voiceRecognition.stop();
            startBtn.disabled = false;
            stopBtn.disabled = true;
            startBtn.classList.remove('recording');
        });

        function applySuggestion(suggestion) {
            const currentCode = codeDiv.textContent;
            codeDiv.textContent = currentCode + '\n' + suggestion;
        }
    </script>
</body>
</html>