section .data
    ; Window-related keywords
    kw_window db 'विंडोः', 0
    kw_button db 'बटनः', 0
    kw_textbox db 'पाठपेटिका', 0
    
    ; Event types
    evt_click db 'क्लिक', 0
    evt_keypress db 'कुंजीदाब', 0
    evt_mousemove db 'मूषकगति', 0
    
    ; Error messages
    error_create_window db 'त्रुटि: Cannot create window', 0xA
    error_create_window_len equ $ - error_create_window
    error_widget db 'त्रुटि: Widget error', 0xA
    error_widget_len equ $ - error_widget

    ; Window properties
    default_width equ 800
    default_height equ 600
    max_widgets equ 100
    
    ; Style constants
    WS_OVERLAPPEDWINDOW equ 0x00CF0000
    WS_VISIBLE equ 0x10000000
    
    ; Window class name
    window_class db 'संस्कृतGUIClass', 0
    
section .bss
    ; Window handle storage
    h_window resd 1
    h_instance resd 1
    
    ; Widget storage
    widget_handles resd max_widgets
    widget_types resb max_widgets
    widget_count resd 1
    
    ; Event callback storage
    callback_table resd max_widgets
    
section .text
    global window_init
    global create_window
    global create_button
    global create_textbox
    global process_events
    
; Initialize GUI system
window_init:
    push ebp
    mov ebp, esp
    
    ; Get module handle
    push 0
    call [GetModuleHandle]
    mov [h_instance], eax
    
    ; Register window class
    call register_window_class
    
    ; Initialize widget count
    mov dword [widget_count], 0
    
    mov esp, ebp
    pop ebp
    ret

; Register the window class
register_window_class:
    push ebp
    mov ebp, esp
    
    ; Fill WNDCLASSEX structure
    sub esp, 48     ; sizeof(WNDCLASSEX)
    mov dword [esp], 48    ; cbSize
    mov dword [esp+4], 3   ; style (CS_HREDRAW | CS_VREDRAW)
    mov eax, window_proc
    mov dword [esp+8], eax ; lpfnWndProc
    mov dword [esp+12], 0  ; cbClsExtra
    mov dword [esp+16], 0  ; cbWndExtra
    mov eax, [h_instance]
    mov dword [esp+20], eax ; hInstance
    push 0
    call [LoadIcon]
    mov dword [esp+24], eax ; hIcon
    push 32512    ; IDC_ARROW
    push 0
    call [LoadCursor]
    mov dword [esp+28], eax ; hCursor
    mov dword [esp+32], 6  ; hbrBackground (COLOR_WINDOW+1)
    mov dword [esp+36], 0  ; lpszMenuName
    mov eax, window_class
    mov dword [esp+40], eax ; lpszClassName
    mov dword [esp+44], 0  ; hIconSm
    
    ; Register class
    lea eax, [esp]
    push eax
    call [RegisterClassEx]
    
    mov esp, ebp
    pop ebp
    ret

; Window procedure
window_proc:
    push ebp
    mov ebp, esp
    
    ; Handle messages
    mov eax, [ebp+12]    ; message
    cmp eax, 2           ; WM_DESTROY
    je .wm_destroy
    cmp eax, 256         ; WM_KEYDOWN
    je .wm_keydown
    cmp eax, 513         ; WM_LBUTTONDOWN
    je .wm_lbuttondown
    
    ; Default processing
    push dword [ebp+20]
    push dword [ebp+16]
    push dword [ebp+12]
    push dword [ebp+8]
    call [DefWindowProc]
    jmp .done
    
.wm_destroy:
    push 0
    call [PostQuitMessage]
    xor eax, eax
    jmp .done
    
.wm_keydown:
    ; Handle key press
    call handle_keypress
    xor eax, eax
    jmp .done
    
.wm_lbuttondown:
    ; Handle mouse click
    call handle_click
    xor eax, eax
    
.done:
    mov esp, ebp
    pop ebp
    ret 16

; Create main window
create_window:
    push ebp
    mov ebp, esp
    
    ; Create window
    push 0          ; lpParam
    push dword [h_instance] ; hInstance
    push 0          ; hMenu
    push 0          ; hWndParent
    push default_height ; nHeight
    push default_width  ; nWidth
    push 100        ; y
    push 100        ; x
    push dword WS_OVERLAPPEDWINDOW | WS_VISIBLE ; dwStyle
    push dword [ebp+8]  ; window title
    push window_class   ; class name
    push 0          ; dwExStyle
    call [CreateWindowEx]
    
    ; Store window handle
    mov [h_window], eax
    
    test eax, eax
    jz .error
    
    ; Show window
    push 1          ; nCmdShow (SW_SHOW)
    push eax
    call [ShowWindow]
    
    mov esp, ebp
    pop ebp
    ret
    
.error:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_create_window
    mov edx, error_create_window_len
    int 0x80
    ret

; Create button widget
create_button:
    push ebp
    mov ebp, esp
    
    ; Create button control
    push 0          ; lpParam
    push dword [h_instance] ; hInstance
    push 0          ; hMenu
    push dword [h_window] ; hWndParent
    push 30         ; nHeight
    push 100        ; nWidth
    push dword [ebp+12] ; y
    push dword [ebp+16] ; x
    push 0x50000000 ; dwStyle (WS_CHILD | WS_VISIBLE)
    push dword [ebp+8]  ; button text
    push dword 'BUTTON' ; class name
    push 0          ; dwExStyle
    call [CreateWindowEx]
    
    ; Store button handle
    mov ecx, [widget_count]
    mov [widget_handles + ecx*4], eax
    mov byte [widget_types + ecx], 1  ; 1 = button type
    inc dword [widget_count]
    
    mov esp, ebp
    pop ebp
    ret

; Create textbox widget
create_textbox:
    push ebp
    mov ebp, esp
    
    ; Create edit control
    push 0          ; lpParam
    push dword [h_instance] ; hInstance
    push 0          ; hMenu
    push dword [h_window] ; hWndParent
    push 25         ; nHeight
    push 200        ; nWidth
    push dword [ebp+12] ; y
    push dword [ebp+16] ; x
    push 0x50800000 ; dwStyle (WS_CHILD | WS_VISIBLE | WS_BORDER)
    push 0          ; window text
    push dword 'EDIT' ; class name
    push 0          ; dwExStyle
    call [CreateWindowEx]
    
    ; Store textbox handle
    mov ecx, [widget_count]
    mov [widget_handles + ecx*4], eax
    mov byte [widget_types + ecx], 2  ; 2 = textbox type
    inc dword [widget_count]
    
    mov esp, ebp
    pop ebp
    ret

; Process window events
process_events:
    push ebp
    mov ebp, esp
    sub esp, 28     ; sizeof(MSG)
    
.message_loop:
    ; Get message
    lea eax, [esp]
    push 0          ; wMsgFilterMax
    push 0          ; wMsgFilterMin
    push 0          ; hWnd
    push eax        ; lpMsg
    call [GetMessage]
    
    test eax, eax
    jz .done
    
    ; Translate and dispatch message
    lea eax, [esp]
    push eax
    call [TranslateMessage]
    
    lea eax, [esp]
    push eax
    call [DispatchMessage]
    
    jmp .message_loop
    
.done:
    mov esp, ebp
    pop ebp
    ret