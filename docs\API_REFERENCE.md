# Sanskrit Programming Language API Reference

## Standard Library Functions

### Input/Output
- `मुद्रणम्(पाठः)` - Prints text to console
  - Parameters: पाठः (text to print)
  - Returns: None

### String Operations
- `विभाजनम्(चिह्नम्)` - Splits text by delimiter
  - Parameters: चिह्नम् (delimiter)
  - Returns: Array of substrings

- `सूची` - Converts string to character array
  - Returns: Array of characters

### Mathematical Operations
- `योगः(क, ख)` - Addition
  - Parameters: क, ख (numbers)
  - Returns: Sum of numbers

- `अन्तरम्(क, ख)` - Subtraction
  - Parameters: क, ख (numbers)
  - Returns: Difference of numbers

- `गुणनम्(क, ख)` - Multiplication
  - Parameters: क, ख (numbers)
  - Returns: Product of numbers

- `भागः(क, ख)` - Division
  - Parameters: क, ख (numbers)
  - Returns: Quotient of numbers

### Array Operations
- `संख्या` - Length of array or string
  - Returns: Number of elements

## Language Features

### Variables
```sanskrit
नाम = मूल्यम्  # Variable assignment
```

### Functions
```sanskrit
क्रिया = (प्राचलः) -> {
    # Function body
    प्रतिफलम् फलम्
}
```

### Control Flow
```sanskrit
yadi (शर्तः) {
    # Conditional block
}

yावत् (शर्तः) {
    # Loop block
}
```

### Error Handling
```sanskrit
प्रयत्नः {
    # Try block
} दोषः (त्रुटिः) {
    # Error handling
}
```

## Best Practices
1. Use descriptive Sanskrit variable names
2. Add comments for complex logic
3. Handle errors appropriately
4. Break complex operations into functions
5. Follow consistent indentation

## Examples
Refer to the `examples` directory for practical demonstrations of these features:
- `text_processor.snkt` - Text manipulation
- `calculator.snkt` - Mathematical operations
- `advanced_features.snkt` - Advanced language features