; Error Handler Module for Sanskrit Programming Language
section .data
    ; Error message templates in English
    err_syntax_en db 'Syntax error: %s', 0
    err_runtime_en db 'Runtime error: %s', 0
    err_syscall_en db 'System error: %s', 0
    err_position db ' at line %d, character %d', 0
    
    ; Error message templates in Sanskrit (UTF-8 encoded)
    err_syntax_sa db 'वाक्यरचना त्रुटि: %s', 0
    err_runtime_sa db 'कार्यकाल त्रुटि: %s', 0
    err_syscall_sa db 'तन्त्रांश त्रुटि: %s', 0
    
    ; System call error mapping
    syscall_eacces db 'Permission denied | अनुमति नहीं', 0
    syscall_enoent db 'File not found | फ़ाइल नहीं मिली', 0
    syscall_einval db 'Invalid argument | अमान्य तर्क', 0

section .bss
    error_buffer resb 1024
    error_position_buffer resb 256

section .text
    global format_error
    global report_error
    global map_syscall_error

; Format error message with position
format_error:
    push ebp
    mov ebp, esp
    
    ; Parameters:
    ; [ebp + 8] = error type (1=syntax, 2=runtime, 3=syscall)
    ; [ebp + 12] = error message
    ; [ebp + 16] = line number
    ; [ebp + 20] = character position
    
    ; Select error template based on type
    mov eax, [ebp + 8]
    cmp eax, 1
    je .syntax_error
    cmp eax, 2
    je .runtime_error
    jmp .syscall_error
    
    .syntax_error:
        push err_syntax_en
        push err_syntax_sa
        jmp .format_message
    
    .runtime_error:
        push err_runtime_en
        push err_runtime_sa
        jmp .format_message
    
    .syscall_error:
        push err_syscall_en
        push err_syscall_sa
    
    .format_message:
        ; Format bilingual error message
        push dword [ebp + 12]  ; error message
        call format_bilingual
        add esp, 12
        
        ; Add position information
        push dword [ebp + 20]  ; character position
        push dword [ebp + 16]  ; line number
        push err_position
        push error_buffer
        call sprintf
        add esp, 16
    
    mov esp, ebp
    pop ebp
    ret

; Map system call error numbers to messages
map_syscall_error:
    push ebp
    mov ebp, esp
    
    ; Parameter:
    ; [ebp + 8] = errno
    
    mov eax, [ebp + 8]
    
    cmp eax, 13      ; EACCES
    je .eacces
    cmp eax, 2       ; ENOENT
    je .enoent
    cmp eax, 22      ; EINVAL
    je .einval
    jmp .unknown
    
    .eacces:
        mov eax, syscall_eacces
        jmp .done
    .enoent:
        mov eax, syscall_enoent
        jmp .done
    .einval:
        mov eax, syscall_einval
        jmp .done
    .unknown:
        mov eax, 0
    
    .done:
        mov esp, ebp
        pop ebp
        ret

; Report error to stderr
report_error:
    push ebp
    mov ebp, esp
    
    ; Write error message to stderr
    mov eax, 4          ; sys_write
    mov ebx, 2          ; stderr
    mov ecx, error_buffer
    mov edx, 1024       ; max length
    int 0x80
    
    mov esp, ebp
    pop ebp
    ret