section .data
    ; Hindi keywords
    kw_lekh_hi db 'लिखो', 0       ; Print keyword in Hindi
    kw_var_hi db 'चर', 0          ; Variable keyword in Hindi
    kw_if_hi db 'अगर', 0          ; If keyword in Hindi
    kw_else_hi db 'वरना', 0        ; Else keyword in Hindi
    kw_loop_hi db 'दोहराओ', 0      ; Loop keyword in Hindi
    kw_func_hi db 'कार्य', 0        ; Function keyword in Hindi

    ; Error messages in Hindi
    error_msg_hi db 'त्रुटि: अमान्य आदेश', 0xA
    error_len_hi equ $ - error_msg_hi
    stack_overflow_hi db 'त्रुटि: स्टैक ओवरफ्लो', 0xA
    stack_overflow_len_hi equ $ - stack_overflow_hi

section .text
    global init_hindi_keywords
    global kw_lekh_hi, kw_var_hi, kw_if_hi, kw_else_hi, kw_loop_hi, kw_func_hi

; Initialize Hindi language support
init_hindi_keywords:
    push ebp
    mov ebp, esp
    ; Add initialization code here
    mov esp, ebp
    pop ebp
    ret