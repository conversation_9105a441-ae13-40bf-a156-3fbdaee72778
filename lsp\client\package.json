{"name": "sanskrit-vscode", "displayName": "Sanskrit Programming Language", "description": "VS Code extension for Sanskrit Programming Language", "version": "1.0.0", "engines": {"vscode": "^1.75.0"}, "activationEvents": ["onLanguage:sanskrit"], "main": "./extension.js", "contributes": {"languages": [{"id": "sanskrit", "aliases": ["Sanskrit", "sanskrit"], "extensions": [".snkt"], "configuration": "./language-configuration.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "dependencies": {"vscode-languageclient": "^8.1.0"}, "devDependencies": {"@types/vscode": "^1.75.0", "@types/node": "^16.11.7", "typescript": "^5.0.2"}}