# संस्कृत प्रोग्रामिंग भाषा शिक्षण / Sanskrit Programming Language Tutorial

## परिचय / Introduction
संस्कृत प्रोग्रामिंग भाषा एक अनूठी भाषा है जो संस्कृत और हिंदी दोनों में प्रोग्रामिंग की सुविधा प्रदान करती है।
Sanskrit Programming Language is a unique language that supports programming in both Sanskrit and Hindi.

## मूल अवधारणाएं / Basic Concepts

### 1. चर (Variables)
```sanskrit
// संस्कृत में
परिवर्तनीय नाम = "राम"
लेखय(नाम)

// हिंदी में
चर नाम = "राम"
लिखो(नाम)
```

### 2. गणितीय संक्रियाएं / Arithmetic Operations
```sanskrit
// संस्कृत में
परिवर्तनीय संख्या१ = ५
परिवर्तनीय संख्या२ = ३
परिवर्तनीय योग = संख्या१ + संख्या२
लेखय(योग)  // Output: ८

// हिंदी में
चर संख्या१ = ५
चर संख्या२ = ३
चर योग = संख्या१ + संख्या२
लिखो(योग)  // Output: ८
```

### 3. शर्तें / Conditions
```sanskrit
// संस्कृत में
यदि (आयु >= १८) {
    लेखय("वयस्क")
} अन्यथा {
    लेखय("किशोर")
}

// हिंदी में
अगर (आयु >= १८) {
    लिखो("वयस्क")
} वरना {
    लिखो("किशोर")
}
```

### 4. पुनरावृत्ति / Loops
```sanskrit
// संस्कृत में
पुनरावर्तनम् (क = १; क <= ५; क++) {
    लेखय(क)
}

// हिंदी में
दोहराओ (क = १; क <= ५; क++) {
    लिखो(क)
}
```

## अभ्यास / Practice Exercises

1. एक कार्यक्रम लिखें जो १ से १० तक की संख्याओं का योग करे।
   Write a program to calculate the sum of numbers from 1 to 10.

2. एक कार्यक्रम लिखें जो किसी संख्या की गुणन तालिका बनाए।
   Write a program to generate the multiplication table of a number.

## आगे की पढ़ाई / Further Learning
- [API संदर्भ / API Reference](API_REFERENCE.md)
- [भाषा विनिर्देश / Language Specification](LANGUAGE_SPEC.md)
- [उदाहरण / Examples](../examples/)