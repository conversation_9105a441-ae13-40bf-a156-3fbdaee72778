section .data
    ; REST API related keywords and messages
    kw_api db 'सेवा', 0           ; API keyword
    kw_get db 'प्राप्त', 0        ; GET method
    kw_post db 'प्रेषय', 0        ; POST method
    kw_put db 'स्थापय', 0         ; PUT method
    kw_delete db 'विलोप', 0       ; DELETE method

    ; Response status codes
    status_200 db 'HTTP/1.1 200 OK', 0xD, 0xA
    status_201 db 'HTTP/1.1 201 Created', 0xD, 0xA
    status_400 db 'HTTP/1.1 400 Bad Request', 0xD, 0xA
    status_404 db 'HTTP/1.1 404 Not Found', 0xD, 0xA
    status_500 db 'HTTP/1.1 500 Internal Server Error', 0xD, 0xA

    ; Headers
    content_json db 'Content-Type: application/json; charset=UTF-8', 0xD, 0xA
    content_len db 'Content-Length: ', 0
    headers_end db 0xD, 0xA

    ; Error messages
    error_method db 'त्रुटि: Invalid HTTP method', 0xA
    error_route db 'त्रुटि: Route not found', 0xA

    ; Configuration
    max_routes equ 100
    max_handlers equ 100
    buffer_size equ 4096

section .bss
    ; Route storage
    route_table resb max_routes * 16  ; path + method + handler
    route_count resd 1
    
    ; Request/Response buffers
    request_buffer resb buffer_size
    response_buffer resb buffer_size
    json_buffer resb buffer_size

section .text
    global सेवा_आरम्भ      ; api_init
    global मार्ग_योजय       ; add_route
    global अनुरोध_संसाधय    ; handle_request
    global उत्तर_भेजय      ; send_response

; Initialize API module
सेवा_आरम्भ:
    push ebp
    mov ebp, esp
    
    ; Initialize route counter
    mov dword [route_count], 0
    
    mov esp, ebp
    pop ebp
    ret

; Add a new API route
; Parameters: method (ebp+8), path (ebp+12), handler (ebp+16)
मार्ग_योजय:
    push ebp
    mov ebp, esp
    push ebx
    
    ; Check if we can add more routes
    mov eax, [route_count]
    cmp eax, max_routes
    jge route_error
    
    ; Calculate route entry position
    imul eax, 16
    lea edi, [route_table + eax]
    
    ; Store method
    mov eax, [ebp + 8]
    mov [edi], eax
    
    ; Store path
    mov eax, [ebp + 12]
    mov [edi + 4], eax
    
    ; Store handler
    mov eax, [ebp + 16]
    mov [edi + 8], eax
    
    ; Increment route count
    inc dword [route_count]
    
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Handle API request
; Parameters: request_buffer (ebp+8)
अनुरोध_संसाधय:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    ; Parse request method and path
    mov esi, [ebp + 8]
    call parse_request
    
    ; Find matching route
    mov ecx, [route_count]
    mov edi, route_table
find_route:
    push ecx
    push esi
    mov esi, [edi + 4]  ; route path
    call compare_path
    pop esi
    pop ecx
    test eax, eax
    jnz route_found
    add edi, 16
    loop find_route
    
    ; Route not found
    mov eax, 404
    call send_error
    jmp handle_done

route_found:
    ; Call route handler
    push edi            ; Save route entry
    mov eax, [edi + 8]  ; Get handler
    call eax
    pop edi
    
handle_done:
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Send JSON response
; Parameters: status (ebp+8), json_data (ebp+12)
उत्तर_भेजय:
    push ebp
    mov ebp, esp
    
    ; Write status line
    mov eax, [ebp + 8]
    call write_status
    
    ; Write headers
    mov eax, 4
    mov ebx, 1
    mov ecx, content_json
    call write_string
    
    ; Write JSON data
    mov esi, [ebp + 12]
    call write_json
    
    mov esp, ebp
    pop ebp
    ret

route_error:
    mov eax, 4
    mov ebx, 2
    mov ecx, error_route
    call write_string
    mov eax, -1
    ret

; Helper function to write string
write_string:
    push ebp
    mov ebp, esp
    
    ; Calculate string length
    mov edx, 0
str_len:
    cmp byte [ecx + edx], 0
    je str_write
    inc edx
    jmp str_len
    
str_write:
    int 0x80
    
    mov esp, ebp
    pop ebp
    ret