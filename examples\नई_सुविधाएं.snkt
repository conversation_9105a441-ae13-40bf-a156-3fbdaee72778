टिप्पणी नई भाषा सुविधाओं का प्रदर्शन
टिप्पणी New Language Features Demonstration

टिप्पणी 1. Type System with Annotations
परिवर्तनीय नाम: पङ्क्तिः = "राम"
परिवर्तनीय आयु: संख्या = २५
परिवर्तनीय सक्रिय: बूलियनः = सत्य
परिवर्तनीय संख्याएं: सूची<संख्या> = [१, २, ३, ४, ५]

टिप्पणी Function with type annotations
कार्य जोड़ना(क: संख्या, ख: संख्या): संख्या {
    प्रत्यावर्तनम् क + ख
}

टिप्पणी 2. Switch/Match Expressions
परिवर्तनीय दिन: संख्या = ३

चयन दिन {
    १ -> लेखय("सोमवार")
    २ -> लेखय("मंगलवार")
    ३ -> लेखय("बुधवार")
    ४ -> लेखय("गुरुवार")
    ५ -> लेखय("शुक्रवार")
    ६, ७ -> लेखय("सप्ताहांत")
    _ -> लेखय("अवैध दिन")
}

टिप्पणी Pattern matching with guards
परिवर्तनीय डेटा = [१, २, ३, ४, ५]

मिलान डेटा {
    सूची(प्रथम, ...शेष) यदि प्रथम > ० -> {
        लेखय("सकारात्मक प्रथम तत्व: " + प्रथम)
        लेखय("शेष तत्व: " + शेष)
    }
    सूची() -> लेखय("खाली सूची")
    संख्या(x) यदि x > १० -> लेखय("बड़ी संख्या: " + x)
    _ -> लेखय("अन्य प्रकार")
}

टिप्पणी 3. Exception Handling
कार्य जोखिमभरा_कार्य(): संख्या {
    परिवर्तनीय परिणाम: संख्या = १० / ०  टिप्पणी This will throw division by zero
    प्रत्यावर्तनम् परिणाम
}

प्रयत्नः {
    परिवर्तनीय परिणाम: संख्या = जोखिमभरा_कार्य()
    लेखय("परिणाम: " + परिणाम)
} ग्रहणम् त्रुटि(संदेश: पङ्क्तिः) {
    लेखय("त्रुटि पकड़ी गई: " + संदेश)
} अन्ततः {
    लेखय("सफाई कार्य पूर्ण")
}

टिप्पणी Custom exception throwing
कार्य उम्र_जांच(उम्र: संख्या): शून्य {
    यदि उम्र < ० {
        फेंकना त्रुटि("नकारात्मक उम्र अवैध है")
    }
    यदि उम्र > १५० {
        फेंकना त्रुटि("उम्र बहुत अधिक है")
    }
    लेखय("वैध उम्र: " + उम्र)
}

प्रयत्नः {
    उम्र_जांच(-५)
} ग्रहणम् त्रुटि(msg) {
    लेखय("उम्र त्रुटि: " + msg)
}

टिप्पणी 4. Class Inheritance
वर्ग पशु {
    परिवर्तनीय नाम: पङ्क्तिः
    परिवर्तनीय आयु: संख्या
    
    कार्य प्रारम्भ(नाम: पङ्क्तिः, आयु: संख्या): शून्य {
        यह.नाम = नाम
        यह.आयु = आयु
    }
    
    कार्य आवाज(): पङ्क्तिः {
        प्रत्यावर्तनम् "सामान्य पशु आवाज"
    }
    
    कार्य जानकारी(): शून्य {
        लेखय("नाम: " + यह.नाम + ", आयु: " + यह.आयु)
    }
}

वर्ग कुत्ता वंश पशु {
    परिवर्तनीय नस्ल: पङ्क्तिः
    
    कार्य प्रारम्भ(नाम: पङ्क्तिः, आयु: संख्या, नस्ल: पङ्क्तिः): शून्य {
        अधिकार.प्रारम्भ(नाम, आयु)  टिप्पणी Call parent constructor
        यह.नस्ल = नस्ल
    }
    
    कार्य आवाज(): पङ्क्तिः {
        प्रत्यावर्तनम् "भौं भौं"
    }
    
    कार्य दौड़ना(): शून्य {
        लेखय(यह.नाम + " दौड़ रहा है!")
    }
}

वर्ग बिल्ली वंश पशु {
    कार्य आवाज(): पङ्क्तिः {
        प्रत्यावर्तनम् "म्याऊं"
    }
    
    कार्य चढ़ना(): शून्य {
        लेखय(यह.नाम + " पेड़ पर चढ़ रही है!")
    }
}

टिप्पणी Using inheritance
परिवर्तनीय मेरा_कुत्ता: कुत्ता = कुत्ता("टॉमी", ३, "लैब्राडोर")
परिवर्तनीय मेरी_बिल्ली: बिल्ली = बिल्ली("मिट्टी", २)

मेरा_कुत्ता.जानकारी()
लेखय("कुत्ते की आवाज: " + मेरा_कुत्ता.आवाज())
मेरा_कुत्ता.दौड़ना()

मेरी_बिल्ली.जानकारी()
लेखय("बिल्ली की आवाज: " + मेरी_बिल्ली.आवाज())
मेरी_बिल्ली.चढ़ना()

टिप्पणी 5. Generic Types and Union Types
कार्य सामान्य_फ़ंक्शन<T>(मान: T): T {
    लेखय("प्राप्त मान: " + मान)
    प्रत्यावर्तनम् मान
}

टिप्पणी Union type example
परिवर्तनीय मिश्रित_मान: संख्या | पङ्क्तिः = "हैलो"
मिश्रित_मान = ४२

टिप्पणी Optional type example
परिवर्तनीय वैकल्पिक_नाम: पङ्क्तिः? = शून्य
वैकल्पिक_नाम = "राम"

यदि वैकल्पिक_नाम != शून्य {
    लेखय("नाम मिला: " + वैकल्पिक_नाम)
}

टिप्पणी 6. Enhanced Standard Library Usage
आयातय "गणितम्"
आयातय "पाठ्यप्रक्रिया"
आयातय "समयः"

परिवर्तनीय वर्गमूल: संख्या = गणितम्.वर्गमूल(१६)
लेखय("१६ का वर्गमूल: " + वर्गमूल)

परिवर्तनीय वाक्य: पङ्क्तिः = "नमस्ते संसार"
परिवर्तनीय शब्द: सूची<पङ्क्तिः> = पाठ्यप्रक्रिया.विभाजन(वाक्य, " ")
लेखय("शब्द: " + शब्द)

परिवर्तनीय वर्तमान_समय: समयः = समयः.अब()
लेखय("वर्तमान समय: " + वर्तमान_समय.स्ट्रिंग())

टिप्पणी 7. Testing Framework Usage
आयातय "परीक्षण_ढांचा"

परीक्षा_समूह("गणित परीक्षण") {
    परीक्षणः("जोड़ना परीक्षण") {
        परिवर्तनीय परिणाम: संख्या = जोड़ना(२, ३)
        अपेक्षित_समान(५, परिणाम, "२ + ३ = ५ होना चाहिए")
    }
    
    परीक्षणः("भाग परीक्षण") {
        प्रयत्नः {
            परिवर्तनीय परिणाम: संख्या = १० / ०
            अपेक्षित_मिथ्या(सत्य, "शून्य से भाग त्रुटि फेंकना चाहिए")
        } ग्रहणम् त्रुटि(msg) {
            अपेक्षित_सत्य(सत्य, "त्रुटि सही तरीके से पकड़ी गई")
        }
    }
}

लेखय("सभी नई सुविधाओं का प्रदर्शन पूर्ण!")
