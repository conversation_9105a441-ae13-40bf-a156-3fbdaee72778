; Simple 2D game demo using the Sanskrit game engine
; Demonstrates sprite movement, collision detection, and input handling

आरम्भ                  ; Initialize game engine

; Create player sprite
चित्रक "player" {
    x = 400
    y = 300
    width = 32
    height = 32
    texture = "player.png"
}

; Create enemy sprites
चित्रक "enemy1" {
    x = 100
    y = 100
    width = 32
    height = 32
    texture = "enemy.png"
}

चित्रक "enemy2" {
    x = 700
    y = 100
    width = 32
    height = 32
    texture = "enemy.png"
}

; Main game loop
पुनरावर्तनम् {
    ; Handle input
    निवेश {
        यदि key == 'ऊपर' {
            player.y -= 5
        }
        यदि key == 'नीचे' {
            player.y += 5
        }
        यदि key == 'बायाँ' {
            player.x -= 5
        }
        यदि key == 'दायाँ' {
            player.x += 5
        }
    }

    ; Check collisions
    संघर्ष player enemy1 {
        लेखय "Game Over!"
        समाप्त
    }

    संघर्ष player enemy2 {
        लेखय "Game Over!"
        समाप्त
    }

    ; Update game state
    अद्यतन

    ; Render frame
    चित्रण
}