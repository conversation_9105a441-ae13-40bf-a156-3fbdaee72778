# Sanskrit Programming Language Interpreter

A simple interpreter for executing Sanskrit programming language commands using Assembly language.

## Features

- Basic Sanskrit command execution (लेखय - print)
- Variable declaration and management (परिवर्तनीय)
- Conditional statements (यदि)
- File-based code execution

## Setup Instructions

### Prerequisites

- NASM (Netwide Assembler)
- LD (GNU Linker)
- GDB (GNU Debugger)

### Building the Project

```bash
nasm -f elf32 sanskrit.asm -o sanskrit.o
ld -m elf_i386 sanskrit.o -o sanskrit
```

### Running the Interpreter

```bash
./sanskrit
```

## Example Code

Create a file with `.snkt` extension:

```sanskrit
लेखय("जय संस्कृतम्")
परिवर्तनीय x = १०
यदि (x > ५) {
    लेखय("बढी संख्या")
}
```

## Command Reference

- `लेखय` - Print text or variable value
- `परिवर्तनीय` - Declare a variable
- `यदि` - If condition

## Contributing

Contributions are welcome! Feel free to submit issues and pull requests.

## License

MIT License