टिप्पणी उन्नत भाषा सुविधाओं का उदाहरण

टिप्पणी वर्ग परिभाषा का उदाहरण
वर्ग जीव {
    परिवर्तनीय नाम = ""
    परिवर्तनीय आयु = ०
    
    क्रिया विवरण() {
        लेखय("नाम: " + नाम)
        लेखय("आयु: " + आयु)
    }
}

टिप्पणी वंश का उदाहरण
वर्ग मनुष्य वंश जीव {
    परिवर्तनीय व्यवसाय = ""
    
    क्रिया विवरण() {
        लेखय("नाम: " + नाम)
        लेखय("आयु: " + आयु)
        लेखय("व्यवसाय: " + व्यवसाय)
    }
}

टिप्पणी नए संचालकों का उदाहरण
परिवर्तनीय क = २
परिवर्तनीय ख = ३

लेखय("घात: " + (क ^ ख))  टिप्पणी २^३ = ८
लेखय("मॉड: " + (७ % ३))  टिप्पणी ७%३ = १

टिप्पणी स्थगय और अनुवर्तन का उदाहरण
परिवर्तनीय ग = १
पुनरावर्तनम् ग <= ५ {
    यदि ग == ३ {
        ग = ग + १
        अनुवर्तन
    }
    
    यदि ग == ५ {
        स्थगय
    }
    
    लेखय(ग)
    ग = ग + १
}