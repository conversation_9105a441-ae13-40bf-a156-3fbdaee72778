section .data
    ; Tree-related keywords
    kw_tree db 'वृक्ष', 0           ; Tree in Sanskrit
    kw_tree_hi db 'पेड़', 0          ; Tree in Hindi
    kw_node db 'शाखा', 0            ; Node in Sanskrit
    kw_node_hi db 'नोड', 0          ; Node in Hindi
    kw_insert db 'योजय', 0          ; Insert
    kw_delete db 'विलोप', 0          ; Delete
    
    ; Graph-related keywords
    kw_graph db 'आलेख', 0           ; Graph in Sanskrit
    kw_graph_hi db 'ग्राफ़', 0        ; Graph in Hindi
    kw_vertex db 'शीर्ष', 0          ; Vertex
    kw_edge db 'रेखा', 0             ; Edge
    
    ; Hash table keywords
    kw_hash db 'अनुक्रम', 0          ; Hash in Sanskrit
    kw_hash_hi db 'हैश', 0           ; Hash in Hindi
    kw_table db 'सारणी', 0           ; Table
    
    ; Error messages
    error_full db 'त्रुटि: Structure is full', 0xA
    error_full_len equ $ - error_full
    error_empty db 'त्रुटि: Structure is empty', 0xA
    error_empty_len equ $ - error_empty
    error_notfound db 'त्रुटि: Element not found', 0xA
    error_notfound_len equ $ - error_notfound

section .bss
    ; Tree structure
    max_nodes equ 1000
    tree_nodes resb max_nodes * 16   ; Each node: value(4), left(4), right(4), parent(4)
    tree_count resd 1
    
    ; Graph structure
    max_vertices equ 100
    max_edges equ 1000
    adj_matrix resb max_vertices * max_vertices
    vertex_list resb max_vertices * 4
    vertex_count resd 1
    
    ; Hash table structure
    table_size equ 997               ; Prime number for better distribution
    hash_table resb table_size * 8   ; Each entry: key(4), value(4)
    table_count resd 1

section .text
    global tree_init
    global tree_insert
    global tree_delete
    global tree_search
    global graph_init
    global graph_add_vertex
    global graph_add_edge
    global hash_init
    global hash_insert
    global hash_search

; Tree operations
tree_init:
    mov dword [tree_count], 0
    ret

tree_insert:
    push ebp
    mov ebp, esp
    push ebx
    push ecx
    
    ; Check if tree is full
    mov eax, [tree_count]
    cmp eax, max_nodes
    jge tree_full_error
    
    ; Create new node
    mov ebx, eax
    imul ebx, 16                    ; Calculate node offset
    add ebx, tree_nodes
    mov eax, [ebp + 8]              ; Get value parameter
    mov [ebx], eax                  ; Store value
    mov dword [ebx + 4], 0          ; Initialize left child
    mov dword [ebx + 8], 0          ; Initialize right child
    mov dword [ebx + 12], 0         ; Initialize parent
    
    inc dword [tree_count]
    
    pop ecx
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Hash table operations
hash_init:
    push ebp
    mov ebp, esp
    
    mov dword [table_count], 0
    xor eax, eax
    mov ecx, table_size

clear_loop:
    mov [hash_table + eax * 8], dword 0
    mov [hash_table + eax * 8 + 4], dword 0
    inc eax
    loop clear_loop
    
    mov esp, ebp
    pop ebp
    ret

; Hash function (simple multiplication method)
hash_function:
    push ebp
    mov ebp, esp
    
    mov eax, [ebp + 8]              ; Get key
    imul eax, 2654435761            ; Multiply by golden ratio
    xor edx, edx
    mov ecx, table_size
    div ecx                         ; Take modulo with table size
    mov eax, edx                    ; Return hash value
    
    mov esp, ebp
    pop ebp
    ret

tree_full_error:
    mov eax, 4
    mov ebx, 2
    mov ecx, error_full
    mov edx, error_full_len
    int 0x80
    ret