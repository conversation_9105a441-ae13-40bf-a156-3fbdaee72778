#!/usr/bin/env python3
"""
Sanskrit Programming Language Compiler (splc)
संस्कृत प्रोग्रामिंग भाषा संकलक

A command-line compiler for Sanskrit Programming Language
Similar to gcc, python3, or rustc
"""

import os
import sys
import argparse
import subprocess
import tempfile
import shutil
import json
from pathlib import Path
from typing import List, Dict, Optional
import platform

class SPLCompiler:
    """Sanskrit Programming Language Compiler"""
    
    def __init__(self):
        self.version = "1.0.0"
        self.target_platforms = ["linux", "windows", "macos", "android", "wasm"]
        self.output_formats = ["executable", "library", "object", "assembly", "wasm"]
        self.optimization_levels = ["O0", "O1", "O2", "O3", "Os"]
        
        # Compiler paths
        self.spl_home = os.environ.get('SPL_HOME', '/usr/local/spl')
        self.lib_path = os.path.join(self.spl_home, 'lib')
        self.include_path = os.path.join(self.spl_home, 'include')
        self.runtime_path = os.path.join(self.spl_home, 'runtime')
        
        # Temporary directory for compilation
        self.temp_dir = None
        
    def compile_file(self, source_file: str, output_file: str = None, 
                    target: str = None, optimization: str = "O1",
                    output_format: str = "executable", 
                    include_dirs: List[str] = None,
                    library_dirs: List[str] = None,
                    libraries: List[str] = None,
                    verbose: bool = False) -> bool:
        """Compile a Sanskrit source file"""
        
        try:
            # Setup compilation environment
            self.temp_dir = tempfile.mkdtemp(prefix='spl_compile_')
            
            if verbose:
                print(f"🔧 Compiling {source_file}")
                print(f"📁 Temporary directory: {self.temp_dir}")
            
            # Determine target platform
            if target is None:
                target = self.detect_target_platform()
            
            # Determine output file name
            if output_file is None:
                output_file = self.generate_output_filename(source_file, output_format, target)
            
            # Compilation pipeline
            success = self.run_compilation_pipeline(
                source_file, output_file, target, optimization,
                output_format, include_dirs, library_dirs, libraries, verbose
            )
            
            if success:
                print(f"✅ Compilation successful: {output_file}")
            else:
                print(f"❌ Compilation failed")
                
            return success
            
        except Exception as e:
            print(f"❌ Compilation error: {e}")
            return False
        finally:
            # Cleanup temporary directory
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
    
    def run_compilation_pipeline(self, source_file: str, output_file: str,
                               target: str, optimization: str, output_format: str,
                               include_dirs: List[str], library_dirs: List[str],
                               libraries: List[str], verbose: bool) -> bool:
        """Run the complete compilation pipeline"""
        
        # Step 1: Lexical Analysis
        if verbose:
            print("🔍 Step 1: Lexical Analysis")
        tokens_file = os.path.join(self.temp_dir, "tokens.json")
        if not self.run_lexer(source_file, tokens_file, verbose):
            return False
        
        # Step 2: Syntax Analysis
        if verbose:
            print("🌳 Step 2: Syntax Analysis")
        ast_file = os.path.join(self.temp_dir, "ast.json")
        if not self.run_parser(tokens_file, ast_file, verbose):
            return False
        
        # Step 3: Semantic Analysis
        if verbose:
            print("🧠 Step 3: Semantic Analysis")
        analyzed_ast_file = os.path.join(self.temp_dir, "analyzed_ast.json")
        if not self.run_semantic_analyzer(ast_file, analyzed_ast_file, include_dirs, verbose):
            return False
        
        # Step 4: Code Generation
        if verbose:
            print("⚙️ Step 4: Code Generation")
        if output_format == "wasm":
            return self.generate_wasm(analyzed_ast_file, output_file, optimization, verbose)
        elif output_format == "assembly":
            return self.generate_assembly(analyzed_ast_file, output_file, target, optimization, verbose)
        elif output_format == "object":
            return self.generate_object(analyzed_ast_file, output_file, target, optimization, verbose)
        elif output_format == "library":
            return self.generate_library(analyzed_ast_file, output_file, target, optimization, libraries, verbose)
        else:  # executable
            return self.generate_executable(analyzed_ast_file, output_file, target, optimization, 
                                          library_dirs, libraries, verbose)
    
    def run_lexer(self, source_file: str, tokens_file: str, verbose: bool) -> bool:
        """Run the lexical analyzer"""
        lexer_script = os.path.join(self.spl_home, 'bin', 'spl_lexer.py')
        cmd = [sys.executable, lexer_script, source_file, tokens_file]
        
        if verbose:
            cmd.append('--verbose')
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Lexer error: {result.stderr}")
            return False
        
        if verbose and result.stdout:
            print(result.stdout)
        
        return True
    
    def run_parser(self, tokens_file: str, ast_file: str, verbose: bool) -> bool:
        """Run the syntax analyzer"""
        parser_script = os.path.join(self.spl_home, 'bin', 'spl_parser.py')
        cmd = [sys.executable, parser_script, tokens_file, ast_file]
        
        if verbose:
            cmd.append('--verbose')
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Parser error: {result.stderr}")
            return False
        
        if verbose and result.stdout:
            print(result.stdout)
        
        return True
    
    def run_semantic_analyzer(self, ast_file: str, analyzed_ast_file: str, 
                            include_dirs: List[str], verbose: bool) -> bool:
        """Run the semantic analyzer"""
        semantic_script = os.path.join(self.spl_home, 'bin', 'spl_semantic.py')
        cmd = [sys.executable, semantic_script, ast_file, analyzed_ast_file]
        
        # Add include directories
        if include_dirs:
            for include_dir in include_dirs:
                cmd.extend(['--include', include_dir])
        
        # Add standard library path
        cmd.extend(['--include', self.include_path])
        
        if verbose:
            cmd.append('--verbose')
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Semantic analysis error: {result.stderr}")
            return False
        
        if verbose and result.stdout:
            print(result.stdout)
        
        return True
    
    def generate_wasm(self, ast_file: str, output_file: str, optimization: str, verbose: bool) -> bool:
        """Generate WebAssembly output"""
        wasm_generator = os.path.join(self.spl_home, 'bin', 'spl_wasm_gen.py')
        cmd = [sys.executable, wasm_generator, ast_file, output_file]
        
        cmd.extend(['--optimization', optimization])
        
        if verbose:
            cmd.append('--verbose')
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"WebAssembly generation error: {result.stderr}")
            return False
        
        return True
    
    def generate_assembly(self, ast_file: str, output_file: str, target: str, 
                         optimization: str, verbose: bool) -> bool:
        """Generate assembly output"""
        asm_generator = os.path.join(self.spl_home, 'bin', 'spl_asm_gen.py')
        cmd = [sys.executable, asm_generator, ast_file, output_file]
        
        cmd.extend(['--target', target])
        cmd.extend(['--optimization', optimization])
        
        if verbose:
            cmd.append('--verbose')
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Assembly generation error: {result.stderr}")
            return False
        
        return True
    
    def generate_object(self, ast_file: str, output_file: str, target: str,
                       optimization: str, verbose: bool) -> bool:
        """Generate object file"""
        # First generate assembly
        asm_file = os.path.join(self.temp_dir, "output.s")
        if not self.generate_assembly(ast_file, asm_file, target, optimization, verbose):
            return False
        
        # Then assemble to object file
        if target.startswith("linux") or target.startswith("macos"):
            assembler = "as"
            cmd = [assembler, "-o", output_file, asm_file]
        elif target.startswith("windows"):
            assembler = "ml64" if "x64" in target else "ml"
            cmd = [assembler, "/c", "/Fo" + output_file, asm_file]
        else:
            print(f"Unsupported target for object generation: {target}")
            return False
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Assembly error: {result.stderr}")
            return False
        
        return True
    
    def generate_library(self, ast_file: str, output_file: str, target: str,
                        optimization: str, libraries: List[str], verbose: bool) -> bool:
        """Generate library file"""
        # First generate object file
        obj_file = os.path.join(self.temp_dir, "output.o")
        if not self.generate_object(ast_file, obj_file, target, optimization, verbose):
            return False
        
        # Create library
        if target.startswith("linux") or target.startswith("macos"):
            archiver = "ar"
            cmd = [archiver, "rcs", output_file, obj_file]
        elif target.startswith("windows"):
            archiver = "lib"
            cmd = [archiver, "/OUT:" + output_file, obj_file]
        else:
            print(f"Unsupported target for library generation: {target}")
            return False
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Library creation error: {result.stderr}")
            return False
        
        return True
    
    def generate_executable(self, ast_file: str, output_file: str, target: str,
                          optimization: str, library_dirs: List[str], 
                          libraries: List[str], verbose: bool) -> bool:
        """Generate executable file"""
        # First generate object file
        obj_file = os.path.join(self.temp_dir, "output.o")
        if not self.generate_object(ast_file, obj_file, target, optimization, verbose):
            return False
        
        # Link executable
        if target.startswith("linux") or target.startswith("macos"):
            linker = "ld"
            cmd = [linker, "-o", output_file, obj_file]
            
            # Add runtime library
            runtime_lib = os.path.join(self.runtime_path, "libspl_runtime.a")
            if os.path.exists(runtime_lib):
                cmd.append(runtime_lib)
            
            # Add library directories
            if library_dirs:
                for lib_dir in library_dirs:
                    cmd.extend(["-L", lib_dir])
            
            # Add libraries
            if libraries:
                for lib in libraries:
                    cmd.extend(["-l", lib])
            
        elif target.startswith("windows"):
            linker = "link"
            cmd = [linker, "/OUT:" + output_file, obj_file]
            
            # Add runtime library
            runtime_lib = os.path.join(self.runtime_path, "spl_runtime.lib")
            if os.path.exists(runtime_lib):
                cmd.append(runtime_lib)
            
            # Add library directories
            if library_dirs:
                for lib_dir in library_dirs:
                    cmd.append("/LIBPATH:" + lib_dir)
            
            # Add libraries
            if libraries:
                for lib in libraries:
                    cmd.append(lib + ".lib")
        
        else:
            print(f"Unsupported target for executable generation: {target}")
            return False
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Linking error: {result.stderr}")
            return False
        
        return True
    
    def detect_target_platform(self) -> str:
        """Detect the current target platform"""
        system = platform.system().lower()
        machine = platform.machine().lower()
        
        if system == "linux":
            if machine in ["x86_64", "amd64"]:
                return "linux-x64"
            elif machine in ["i386", "i686"]:
                return "linux-x86"
            elif machine.startswith("arm"):
                return "linux-arm"
        elif system == "windows":
            if machine in ["x86_64", "amd64"]:
                return "windows-x64"
            else:
                return "windows-x86"
        elif system == "darwin":
            if machine == "x86_64":
                return "macos-x64"
            elif machine == "arm64":
                return "macos-arm64"
        
        return "linux-x64"  # Default fallback
    
    def generate_output_filename(self, source_file: str, output_format: str, target: str) -> str:
        """Generate appropriate output filename"""
        base_name = Path(source_file).stem
        
        if output_format == "executable":
            if target.startswith("windows"):
                return base_name + ".exe"
            else:
                return base_name
        elif output_format == "library":
            if target.startswith("windows"):
                return base_name + ".lib"
            else:
                return "lib" + base_name + ".a"
        elif output_format == "object":
            if target.startswith("windows"):
                return base_name + ".obj"
            else:
                return base_name + ".o"
        elif output_format == "assembly":
            return base_name + ".s"
        elif output_format == "wasm":
            return base_name + ".wasm"
        
        return base_name + ".out"
    
    def show_version(self):
        """Show compiler version information"""
        print(f"Sanskrit Programming Language Compiler (splc) {self.version}")
        print(f"संस्कृत प्रोग्रामिंग भाषा संकलक")
        print(f"SPL Home: {self.spl_home}")
        print(f"Supported targets: {', '.join(self.target_platforms)}")
        print(f"Output formats: {', '.join(self.output_formats)}")

def main():
    parser = argparse.ArgumentParser(
        description='Sanskrit Programming Language Compiler',
        prog='splc'
    )
    
    parser.add_argument('source_file', help='Sanskrit source file (.snkt)')
    parser.add_argument('-o', '--output', help='Output file name')
    parser.add_argument('-t', '--target', choices=['linux', 'windows', 'macos', 'android', 'wasm'],
                       help='Target platform')
    parser.add_argument('-f', '--format', choices=['executable', 'library', 'object', 'assembly', 'wasm'],
                       default='executable', help='Output format')
    parser.add_argument('-O', '--optimization', choices=['O0', 'O1', 'O2', 'O3', 'Os'],
                       default='O1', help='Optimization level')
    parser.add_argument('-I', '--include', action='append', dest='include_dirs',
                       help='Include directory')
    parser.add_argument('-L', '--library-dir', action='append', dest='library_dirs',
                       help='Library directory')
    parser.add_argument('-l', '--library', action='append', dest='libraries',
                       help='Link library')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Verbose output')
    parser.add_argument('--version', action='store_true',
                       help='Show version information')
    
    args = parser.parse_args()
    
    compiler = SPLCompiler()
    
    if args.version:
        compiler.show_version()
        return 0
    
    if not os.path.exists(args.source_file):
        print(f"Error: Source file '{args.source_file}' not found")
        return 1
    
    success = compiler.compile_file(
        source_file=args.source_file,
        output_file=args.output,
        target=args.target,
        optimization=args.optimization,
        output_format=args.format,
        include_dirs=args.include_dirs or [],
        library_dirs=args.library_dirs or [],
        libraries=args.libraries or [],
        verbose=args.verbose
    )
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
