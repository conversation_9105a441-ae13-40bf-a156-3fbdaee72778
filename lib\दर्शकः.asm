section .data
    ; Template-related keywords and messages
    kw_template db 'दर्शकः', 0       ; Template keyword
    kw_render db 'प्रदर्शय', 0      ; Render keyword
    kw_var db 'चर', 0              ; Variable interpolation
    kw_if db 'यदि', 0              ; Conditional
    kw_loop db 'पुनरावर्तनम्', 0    ; Loop construct

    ; Error messages
    error_template db 'त्रुटि: Template error', 0xA
    error_template_len equ $ - error_template
    error_syntax db 'त्रुटि: Template syntax error', 0xA
    error_syntax_len equ $ - error_syntax

    ; Template configuration
    max_templates equ 32
    template_size equ 8192
    var_table_size equ 256

section .bss
    ; Template storage
    templates resb max_templates * template_size
    template_count resd 1
    
    ; Variable table for template context
    var_table resb var_table_size
    var_count resd 1
    
    ; Render buffer
    render_buffer resb template_size

section .text
    global दर्शक_आरम्भ     ; template_init
    global दर्शक_लोड       ; template_load
    global दर्शक_प्रदर्शय   ; template_render

; Initialize template engine
दर्शक_आरम्भ:
    push ebp
    mov ebp, esp
    
    ; Initialize counters
    mov dword [template_count], 0
    mov dword [var_count], 0
    
    mov esp, ebp
    pop ebp
    ret

; Load template from file
; Parameters: filename (ebp+8)
; Returns: template ID or -1 on error
दर्शक_लोड:
    push ebp
    mov ebp, esp
    push ebx
    
    ; Check if we can store more templates
    mov eax, [template_count]
    cmp eax, max_templates
    jge load_error
    
    ; Calculate template storage location
    imul eax, template_size
    lea edi, [templates + eax]
    
    ; Open template file
    mov eax, 5          ; sys_open
    mov ebx, [ebp + 8]  ; filename
    mov ecx, 0          ; O_RDONLY
    int 0x80
    
    test eax, eax
    js load_error
    
    ; Read template content
    push eax            ; save file descriptor
    mov ebx, eax
    mov eax, 3          ; sys_read
    mov ecx, edi
    mov edx, template_size
    int 0x80
    
    ; Close file
    pop ebx
    mov eax, 6          ; sys_close
    int 0x80
    
    ; Return template ID
    mov eax, [template_count]
    inc dword [template_count]
    
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Render template with context
; Parameters: template_id (ebp+8), context (ebp+12)
; Returns: pointer to render buffer
दर्शक_प्रदर्शय:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    ; Get template pointer
    mov eax, [ebp + 8]  ; template_id
    imul eax, template_size
    lea esi, [templates + eax]
    
    ; Setup render buffer
    mov edi, render_buffer
    
    ; Process template
render_loop:
    mov al, [esi]
    test al, al
    jz render_done
    
    ; Check for variable interpolation
    cmp al, '{'
    je process_var
    
    ; Copy character
    mov [edi], al
    inc edi
    inc esi
    jmp render_loop

process_var:
    ; Handle variable interpolation
    inc esi            ; Skip {
    call find_var
    add esi, 2         ; Skip }
    jmp render_loop

render_done:
    mov byte [edi], 0  ; Null terminate
    
    ; Return render buffer
    mov eax, render_buffer
    
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

load_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_template
    mov edx, error_template_len
    int 0x80
    mov eax, -1
    ret

; Helper function to find and interpolate variables
find_var:
    push ebp
    mov ebp, esp
    
    ; Compare variable name with context
    mov ecx, [ebp + 12] ; context
    
    ; TODO: Implement variable lookup and value copying
    
    mov esp, ebp
    pop ebp
    ret