#!/usr/bin/env python3
"""
Sanskrit Programming Language Documentation Generator
दस्तावेज़ जनरेटर (Documentation Generator)

This tool automatically generates documentation from Sanskrit source code files.
It parses .snkt files and extracts:
- Function definitions with parameters and return types
- Class definitions with methods and properties
- Module imports and exports
- Comments and docstrings
- Type annotations
"""

import os
import re
import json
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

@dataclass
class Parameter:
    name: str
    type: str
    description: str = ""

@dataclass
class Function:
    name: str
    parameters: List[Parameter]
    return_type: str
    description: str = ""
    examples: List[str] = None
    
    def __post_init__(self):
        if self.examples is None:
            self.examples = []

@dataclass
class Class:
    name: str
    parent: Optional[str]
    methods: List[Function]
    properties: List[Parameter]
    description: str = ""
    
    def __post_init__(self):
        if self.methods is None:
            self.methods = []
        if self.properties is None:
            self.properties = []

@dataclass
class Module:
    name: str
    path: str
    functions: List[Function]
    classes: List[Class]
    imports: List[str]
    description: str = ""
    
    def __post_init__(self):
        if self.functions is None:
            self.functions = []
        if self.classes is None:
            self.classes = []
        if self.imports is None:
            self.imports = []

class SanskritDocGenerator:
    """Documentation generator for Sanskrit Programming Language"""
    
    def __init__(self):
        # Sanskrit keywords and patterns
        self.keywords = {
            'function': r'कार्य|क्रिया',
            'class': r'वर्ग',
            'variable': r'परिवर्तनीय',
            'import': r'आयातय',
            'comment': r'टिप्पणी',
            'return': r'प्रत्यावर्तनम्',
            'inheritance': r'वंश'
        }
        
        # Type mappings
        self.type_mappings = {
            'संख्या': 'Number',
            'पङ्क्तिः': 'String',
            'सूची': 'List',
            'बूलियनः': 'Boolean',
            'कार्य': 'Function',
            'शून्य': 'Void',
            'प्रकार': 'Type'
        }
        
        self.modules = []
    
    def parse_file(self, file_path: str) -> Module:
        """Parse a Sanskrit source file and extract documentation"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        module_name = Path(file_path).stem
        module = Module(
            name=module_name,
            path=file_path,
            functions=[],
            classes=[],
            imports=[]
        )
        
        # Extract module description from top comments
        module.description = self._extract_module_description(content)
        
        # Extract imports
        module.imports = self._extract_imports(content)
        
        # Extract functions
        module.functions = self._extract_functions(content)
        
        # Extract classes
        module.classes = self._extract_classes(content)
        
        return module
    
    def _extract_module_description(self, content: str) -> str:
        """Extract module description from top-level comments"""
        lines = content.split('\n')
        description_lines = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('टिप्पणी') or line.startswith(';'):
                # Remove comment marker
                desc = re.sub(r'^(टिप्पणी|;)\s*', '', line)
                description_lines.append(desc)
            elif line and not line.startswith('टिप्पणी') and not line.startswith(';'):
                break
        
        return '\n'.join(description_lines)
    
    def _extract_imports(self, content: str) -> List[str]:
        """Extract import statements"""
        import_pattern = rf'({self.keywords["import"]})\s+"([^"]+)"'
        matches = re.findall(import_pattern, content)
        return [match[1] for match in matches]
    
    def _extract_functions(self, content: str) -> List[Function]:
        """Extract function definitions"""
        functions = []
        
        # Pattern for function definition with type annotations
        func_pattern = rf'({self.keywords["function"]})\s+([^\s(]+)\s*\(([^)]*)\)\s*:\s*([^\s{{]+)\s*{{'
        
        matches = re.finditer(func_pattern, content)
        
        for match in matches:
            func_name = match.group(2)
            params_str = match.group(3)
            return_type = match.group(4)
            
            # Parse parameters
            parameters = self._parse_parameters(params_str)
            
            # Extract function description from preceding comments
            description = self._extract_preceding_comments(content, match.start())
            
            # Extract examples from comments
            examples = self._extract_examples(content, match.start(), match.end())
            
            function = Function(
                name=func_name,
                parameters=parameters,
                return_type=self._translate_type(return_type),
                description=description,
                examples=examples
            )
            
            functions.append(function)
        
        return functions
    
    def _extract_classes(self, content: str) -> List[Class]:
        """Extract class definitions"""
        classes = []
        
        # Pattern for class definition with optional inheritance
        class_pattern = rf'({self.keywords["class"]})\s+([^\s{{]+)(?:\s+{self.keywords["inheritance"]}\s+([^\s{{]+))?\s*{{'
        
        matches = re.finditer(class_pattern, content)
        
        for match in matches:
            class_name = match.group(2)
            parent_class = match.group(3) if match.group(3) else None
            
            # Find class body
            class_body = self._extract_class_body(content, match.end())
            
            # Extract methods and properties from class body
            methods = self._extract_functions(class_body)
            properties = self._extract_properties(class_body)
            
            # Extract class description
            description = self._extract_preceding_comments(content, match.start())
            
            class_obj = Class(
                name=class_name,
                parent=parent_class,
                methods=methods,
                properties=properties,
                description=description
            )
            
            classes.append(class_obj)
        
        return classes
    
    def _parse_parameters(self, params_str: str) -> List[Parameter]:
        """Parse function parameters with type annotations"""
        parameters = []
        
        if not params_str.strip():
            return parameters
        
        # Split parameters by comma
        param_parts = [p.strip() for p in params_str.split(',')]
        
        for param_part in param_parts:
            if ':' in param_part:
                name, type_str = param_part.split(':', 1)
                name = name.strip()
                type_str = type_str.strip()
            else:
                name = param_part.strip()
                type_str = 'Any'
            
            parameter = Parameter(
                name=name,
                type=self._translate_type(type_str),
                description=""
            )
            parameters.append(parameter)
        
        return parameters
    
    def _extract_properties(self, class_body: str) -> List[Parameter]:
        """Extract class properties"""
        properties = []
        
        # Pattern for property declaration
        prop_pattern = rf'({self.keywords["variable"]})\s+([^\s:]+)\s*:\s*([^\s=]+)'
        
        matches = re.finditer(prop_pattern, class_body)
        
        for match in matches:
            prop_name = match.group(2)
            prop_type = match.group(3)
            
            property_obj = Parameter(
                name=prop_name,
                type=self._translate_type(prop_type),
                description=""
            )
            properties.append(property_obj)
        
        return properties
    
    def _extract_class_body(self, content: str, start_pos: int) -> str:
        """Extract class body content"""
        brace_count = 0
        i = start_pos
        
        # Find opening brace
        while i < len(content) and content[i] != '{':
            i += 1
        
        if i >= len(content):
            return ""
        
        start = i + 1
        brace_count = 1
        i += 1
        
        # Find matching closing brace
        while i < len(content) and brace_count > 0:
            if content[i] == '{':
                brace_count += 1
            elif content[i] == '}':
                brace_count -= 1
            i += 1
        
        return content[start:i-1]
    
    def _extract_preceding_comments(self, content: str, pos: int) -> str:
        """Extract comments preceding a definition"""
        lines = content[:pos].split('\n')
        comment_lines = []
        
        # Go backwards from the position
        for line in reversed(lines):
            line = line.strip()
            if line.startswith('टिप्पणी') or line.startswith(';'):
                desc = re.sub(r'^(टिप्पणी|;)\s*', '', line)
                comment_lines.insert(0, desc)
            elif line:
                break
        
        return '\n'.join(comment_lines)
    
    def _extract_examples(self, content: str, start: int, end: int) -> List[str]:
        """Extract code examples from comments"""
        # This would extract examples from special comment blocks
        # For now, return empty list
        return []
    
    def _translate_type(self, sanskrit_type: str) -> str:
        """Translate Sanskrit type to English"""
        return self.type_mappings.get(sanskrit_type, sanskrit_type)
    
    def generate_markdown(self, modules: List[Module], output_dir: str):
        """Generate Markdown documentation"""
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate index file
        self._generate_index(modules, output_dir)
        
        # Generate individual module documentation
        for module in modules:
            self._generate_module_doc(module, output_dir)
    
    def _generate_index(self, modules: List[Module], output_dir: str):
        """Generate index.md file"""
        index_path = os.path.join(output_dir, 'index.md')
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write("# Sanskrit Programming Language Documentation\n\n")
            f.write("## Modules\n\n")
            
            for module in modules:
                f.write(f"- [{module.name}]({module.name}.md)\n")
                if module.description:
                    f.write(f"  - {module.description.split('.')[0]}\n")
            
            f.write("\n## Quick Reference\n\n")
            f.write("### Keywords\n\n")
            f.write("| Sanskrit | English | Description |\n")
            f.write("|----------|---------|-------------|\n")
            f.write("| कार्य | function | Function definition |\n")
            f.write("| वर्ग | class | Class definition |\n")
            f.write("| परिवर्तनीय | variable | Variable declaration |\n")
            f.write("| यदि | if | Conditional statement |\n")
            f.write("| चयन | switch | Switch expression |\n")
            f.write("| प्रयत्नः | try | Exception handling |\n")
    
    def _generate_module_doc(self, module: Module, output_dir: str):
        """Generate documentation for a single module"""
        doc_path = os.path.join(output_dir, f"{module.name}.md")
        
        with open(doc_path, 'w', encoding='utf-8') as f:
            f.write(f"# {module.name}\n\n")
            
            if module.description:
                f.write(f"{module.description}\n\n")
            
            # Imports
            if module.imports:
                f.write("## Imports\n\n")
                for imp in module.imports:
                    f.write(f"- `{imp}`\n")
                f.write("\n")
            
            # Functions
            if module.functions:
                f.write("## Functions\n\n")
                for func in module.functions:
                    self._write_function_doc(f, func)
            
            # Classes
            if module.classes:
                f.write("## Classes\n\n")
                for cls in module.classes:
                    self._write_class_doc(f, cls)
    
    def _write_function_doc(self, f, func: Function):
        """Write function documentation"""
        f.write(f"### {func.name}\n\n")
        
        if func.description:
            f.write(f"{func.description}\n\n")
        
        # Signature
        params_str = ", ".join([f"{p.name}: {p.type}" for p in func.parameters])
        f.write(f"```sanskrit\n")
        f.write(f"कार्य {func.name}({params_str}): {func.return_type}\n")
        f.write(f"```\n\n")
        
        # Parameters
        if func.parameters:
            f.write("**Parameters:**\n\n")
            for param in func.parameters:
                f.write(f"- `{param.name}` ({param.type})")
                if param.description:
                    f.write(f": {param.description}")
                f.write("\n")
            f.write("\n")
        
        # Return value
        f.write(f"**Returns:** {func.return_type}\n\n")
        
        # Examples
        if func.examples:
            f.write("**Examples:**\n\n")
            for example in func.examples:
                f.write(f"```sanskrit\n{example}\n```\n\n")
    
    def _write_class_doc(self, f, cls: Class):
        """Write class documentation"""
        f.write(f"### {cls.name}\n\n")
        
        if cls.description:
            f.write(f"{cls.description}\n\n")
        
        if cls.parent:
            f.write(f"**Inherits from:** {cls.parent}\n\n")
        
        # Properties
        if cls.properties:
            f.write("**Properties:**\n\n")
            for prop in cls.properties:
                f.write(f"- `{prop.name}`: {prop.type}")
                if prop.description:
                    f.write(f" - {prop.description}")
                f.write("\n")
            f.write("\n")
        
        # Methods
        if cls.methods:
            f.write("**Methods:**\n\n")
            for method in cls.methods:
                self._write_function_doc(f, method)

def main():
    parser = argparse.ArgumentParser(description='Generate documentation for Sanskrit Programming Language')
    parser.add_argument('source_dir', help='Source directory containing .snkt files')
    parser.add_argument('output_dir', help='Output directory for documentation')
    parser.add_argument('--format', choices=['markdown', 'html', 'json'], default='markdown',
                       help='Output format (default: markdown)')
    
    args = parser.parse_args()
    
    generator = SanskritDocGenerator()
    
    # Find all .snkt files
    source_path = Path(args.source_dir)
    snkt_files = list(source_path.glob('**/*.snkt'))
    
    print(f"Found {len(snkt_files)} Sanskrit files")
    
    # Parse all files
    modules = []
    for file_path in snkt_files:
        print(f"Parsing {file_path}")
        try:
            module = generator.parse_file(str(file_path))
            modules.append(module)
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
    
    # Generate documentation
    if args.format == 'markdown':
        generator.generate_markdown(modules, args.output_dir)
        print(f"Documentation generated in {args.output_dir}")
    elif args.format == 'json':
        # Export as JSON
        json_data = [asdict(module) for module in modules]
        json_path = os.path.join(args.output_dir, 'documentation.json')
        os.makedirs(args.output_dir, exist_ok=True)
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        print(f"JSON documentation generated: {json_path}")

if __name__ == '__main__':
    main()
