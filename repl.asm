section .data
    prompt db '>>> ', 0
    prompt_len equ $ - prompt
    buffer_size equ 1024
    newline db 0xA
    error_msg db 'Error: Invalid command', 0xA
    error_len equ $ - error_msg

    ; Try-catch keywords
    kw_try db 'प्रयास', 0
    kw_catch db 'पकड़ो', 0
    kw_finally db 'अंत_में', 0
    kw_throw db 'फेंको', 0
    
    ; Hindi try-catch keywords
    kw_try_hi db 'कोशिश', 0
    kw_catch_hi db 'पकड़ो', 0
    kw_finally_hi db 'आखिरमें', 0
    kw_throw_hi db 'फेंको', 0

    ; Keywords (same as sanskrit.asm)
    kw_lekh db 'लेखय', 0
    kw_var db 'परिवर्तनीय', 0
    kw_if db 'यदि', 0
    kw_else db 'अन्यथा', 0
    kw_loop db 'पुनरावर्तनम्', 0
    kw_func db 'कार्य', 0

    ; Cloud and smart contract keywords
    kw_cloud_init db 'मेघआरम्भ', 0
    kw_deploy db 'प्रक्षेपण', 0
    kw_invoke db 'आह्वान', 0
    kw_cloud_init_hi db 'बादलशुरू', 0
    kw_deploy_hi db 'तैनात', 0
    kw_invoke_hi db 'बुलाओ', 0

    ; Variables section
    var_table_size equ 64
    var_table resb var_table_size
    var_count dd 0

section .bss
    input_buffer resb buffer_size
    condition_stack resb 64  ; Stack for nested conditions
    condition_sp dd 0        ; Stack pointer for conditions
    loop_stack resb 64     ; Stack for nested loops
    loop_count dd 0        ; Loop counter
    
    ; Try-catch state
    try_stack resb 256     ; Stack for try blocks
    try_sp dd 0           ; Try stack pointer
    error_buffer resb 1024 ; Error message buffer

section .data
    extern kw_lekh_hi, kw_var_hi, kw_if_hi, kw_else_hi, kw_loop_hi, kw_func_hi
    error_prefix db 'Error at line ', 0
    error_char_pos db ', character ', 0
    current_line dd 1
    current_char dd 1
    func_table_size equ 64
    func_table resb func_table_size * 16  ; Store function name and entry point
    func_count dd 0
    max_stack_depth equ 1000    ; Maximum call stack depth
    current_stack_depth dd 0    ; Current stack depth counter
    stack_overflow_error db 'त्रुटि: Stack overflow', 0xA
    stack_overflow_len equ $ - stack_overflow_error

section .bss
    call_stack resb max_stack_depth * 4  ; Call stack for function returns

section .text
    global parse_command

_start_repl:
    ; Main REPL loop
repl_loop:
    ; Display prompt
    mov eax, 4          ; sys_write
    mov ebx, 1          ; stdout
    mov ecx, prompt     ; prompt string
    mov edx, prompt_len ; length
    int 0x80

    ; Read user input
    mov eax, 3          ; sys_read
    mov ebx, 0          ; stdin
    mov ecx, input_buffer
    mov edx, buffer_size
    int 0x80

    ; Process input
    mov esi, input_buffer
    call parse_command

    jmp repl_loop

; Parse and execute a single command
parse_command:
    push esi

    ; Check for keywords
    mov edi, kw_var
    call compare_string
    test eax, eax
    jz check_print

    ; Handle variable declaration
    call handle_variable
    jmp parse_done

check_print:
    mov edi, kw_lekh
    call compare_string
    test eax, eax
    jnz handle_print
    
    mov edi, kw_lekh_hi
    call compare_string
    test eax, eax
    jz check_var
    
    call handle_print
    jmp parse_done

check_var:
    mov edi, kw_var
    call compare_string
    test eax, eax
    jnz handle_variable
    
    mov edi, kw_var_hi
    call compare_string
    test eax, eax
    jz check_if
    
    call handle_variable
    jmp parse_done

check_if:
    mov edi, kw_if
    call compare_string
    test eax, eax
    jnz handle_if
    
    mov edi, kw_if_hi
    call compare_string
    test eax, eax
    jz check_else
    
    call handle_if
    jmp parse_done

check_else:
    mov edi, kw_else
    call compare_string
    test eax, eax
    jnz handle_else
    
    mov edi, kw_else_hi
    call compare_string
    test eax, eax
    jz check_loop
    
    call handle_else
    jmp parse_done

check_loop:
    mov edi, kw_loop
    call compare_string
    test eax, eax
    jnz handle_loop
    
    mov edi, kw_loop_hi
    call compare_string
    test eax, eax
    jz check_func
    
    call handle_loop
    jmp parse_done

check_ml:
    ; Check for machine learning operations
    mov edi, kw_train
    call compare_string
    test eax, eax
    jnz handle_ml_train

    mov edi, kw_train_hi
    call compare_string
    test eax, eax
    jnz handle_ml_train

    mov edi, kw_predict
    call compare_string
    test eax, eax
    jnz handle_ml_predict

    mov edi, kw_predict_hi
    call compare_string
    test eax, eax
    jnz handle_ml_predict

    jmp check_mobile

check_mobile:
    ; Check for mobile app operations
    mov edi, kw_app
    call compare_string
    test eax, eax
    jnz handle_mobile_app

    mov edi, kw_app_hi
    call compare_string
    test eax, eax
    jnz handle_mobile_app

    mov edi, kw_screen
    call compare_string
    test eax, eax
    jnz handle_mobile_screen

    mov edi, kw_screen_hi
    call compare_string
    test eax, eax
    jnz handle_mobile_screen

    jmp check_package

check_package:
    ; Check for package management operations
    mov edi, kw_install
    call compare_string
    test eax, eax
    jnz handle_package_install

    mov edi, kw_install_hi
    call compare_string
    test eax, eax
    jnz handle_package_install

    mov edi, kw_remove
    call compare_string
    test eax, eax
    jnz handle_package_remove

    mov edi, kw_remove_hi
    call compare_string
    test eax, eax
    jnz handle_package_remove

    mov edi, kw_update
    call compare_string
    test eax, eax
    jnz handle_package_update

    mov edi, kw_update_hi
    call compare_string
    test eax, eax
    jnz handle_package_update

    jmp check_blockchain

check_blockchain:
    ; Check for blockchain operations
    mov edi, kw_contract
    call compare_string
    test eax, eax
    jnz handle_contract

    mov edi, kw_contract_hi
    call compare_string
    test eax, eax
    jnz handle_contract

    mov edi, kw_tx
    call compare_string
    test eax, eax
    jnz handle_transaction

    mov edi, kw_tx_hi
    call compare_string
    test eax, eax
    jnz handle_transaction

    jmp check_func:
    mov edi, kw_func
    call compare_string
    test eax, eax
    jnz handle_function
    
    mov edi, kw_func_hi
    call compare_string
    test eax, eax
    jz check_nn
    
    call handle_function
    jmp parse_done

check_cloud:
    ; Check for cloud operations
    mov edi, kw_cloud_init
    call compare_string
    test eax, eax
    jnz handle_cloud_init

    mov edi, kw_cloud_init_hi
    call compare_string
    test eax, eax
    jnz handle_cloud_init

    mov edi, kw_deploy
    call compare_string
    test eax, eax
    jnz handle_cloud_deploy

    mov edi, kw_deploy_hi
    call compare_string
    test eax, eax
    jnz handle_cloud_deploy

    mov edi, kw_invoke
    call compare_string
    test eax, eax
    jnz handle_cloud_invoke

    mov edi, kw_invoke_hi
    call compare_string
    test eax, eax
    jnz handle_cloud_invoke

    jmp parse_error

section .text
    ; Update error handling with position information
handle_cloud_init:
    call cloud_init
    test eax, eax
    jz parse_error
    jmp parse_done

handle_cloud_deploy:
    ; Get function name and code
    call skip_whitespace
    push esi    ; Save function name
    call get_token
    pop esi     ; Restore function name
    
    push esi    ; function_name
    push eax    ; function_code
    call cloud_deploy
    add esp, 8
    test eax, eax
    jz parse_error
    jmp parse_done

handle_cloud_invoke:
    ; Get function name and parameters
    call skip_whitespace
    push esi    ; Save function name
    call get_token
    pop esi     ; Restore function name
    
    push esi    ; function_name
    push eax    ; parameters
    call cloud_invoke
    add esp, 8
    test eax, eax
    jz parse_error
    jmp parse_done

parse_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_prefix
    mov edx, 11
    int 0x80
    
    ; Print line number
    mov eax, [current_line]
    call print_number
    
    ; Print character position
    mov eax, 4
    mov ebx, 1
    mov ecx, error_char_pos
    mov edx, 12
    int 0x80
    
    mov eax, [current_char]
    call print_number
    
    mov eax, 4
    mov ebx, 1
    mov ecx, error_msg
    mov edx, error_len
    int 0x80
    jmp parse_done
    pop esi
    ret

; String comparison function
compare_string:
    push ebx
compare_loop:
    mov bl, [esi]
    mov bh, [edi]
    cmp bl, bh
    jne compare_end
    test bl, bl
    jz compare_match
    inc esi
    inc edi
    jmp compare_loop
compare_match:
    mov eax, 1
compare_end:
    pop ebx
    ret

; Handle variable declaration and assignment
handle_variable:
    push esi
    inc esi                    ; Skip space after keyword

    ; Parse variable name
    mov edi, [var_count]
    imul edi, 8               ; Each entry is 8 bytes (name + value)
    add edi, var_table

var_name_loop:
    mov al, [esi]
    cmp al, ' '
    je var_name_done
    cmp al, '='
    je var_name_done
    mov [edi], al
    inc edi
    inc esi
    jmp var_name_loop

var_name_done:
    mov byte [edi], 0        ; Null terminate

    ; Skip to value
find_equals:
    cmp byte [esi], '='
    je parse_value
    inc esi
    jmp find_equals

parse_value:
    inc esi                  ; Skip = sign
    call parse_number
    mov [edi + 4], eax       ; Store value

    ; Increment variable count
    inc dword [var_count]

    pop esi
    ret

; Parse number from string
parse_number:
    xor eax, eax             ; Clear result
parse_digit:
    movzx ecx, byte [esi]    ; Get current digit
    sub ecx, '०'             ; Convert from Devanagari numeral
    jb parse_done            ; Exit if not a digit
    cmp ecx, 9
    ja parse_done
    imul eax, 10             ; Multiply current result by 10
    add eax, ecx             ; Add new digit
    inc esi
    jmp parse_digit
parse_done:
    ret

; Handle print command
handle_print:
    push esi
    inc esi                  ; Skip opening parenthesis

    ; Find variable in table
    mov ecx, [var_count]
    test ecx, ecx
    jz print_error
    mov edi, var_table

find_var:
    push ecx
    push esi
    call compare_string
    pop esi
    pop ecx
    test eax, eax
    jnz print_value
    add edi, 8               ; Move to next variable entry
    loop find_var
    jmp print_error

print_value:
    mov eax, [edi + 4]       ; Get variable value
    push eax
    call print_number
    mov eax, 4               ; Print newline
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80
    pop eax
    pop esi
    ret

print_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_msg
    mov edx, error_len
    int 0x80
    pop esi
    ret

; Print a number
print_number:
    push ebp
    mov ebp, esp
    sub esp, 16              ; Local buffer for digits
    mov eax, [ebp + 8]       ; Get number to print
    mov edi, esp
    add edi, 15              ; Start from end of buffer
    mov byte [edi], 0        ; Null terminate

convert_loop:
    dec edi
    mov edx, 0
    mov ecx, 10
    div ecx
    add dl, '०'              ; Convert to Devanagari numeral
    mov [edi], dl
    test eax, eax
    jnz convert_loop

    ; Print the number
    mov eax, 4
    mov ebx, 1
    mov ecx, edi
    mov edx, esp
    add edx, 15
    sub edx, edi
    int 0x80

    mov esp, ebp
    pop ebp
    ret 4

exit_program:
    mov eax, 1          ; sys_exit
    xor ebx, ebx        ; return 0
    int 0x80

; Removed duplicate check_if and check_else blocks

; Handle if statement
handle_if:
    push esi
    inc esi                    ; Skip space after keyword

    ; Parse condition
    call parse_condition
    
    ; Store condition result
    mov ebx, [condition_sp]
    mov [condition_stack + ebx], al
    inc dword [condition_sp]

    pop esi
    ret

; Handle else statement
handle_else:
    push esi
    
    ; Check if we have a matching if
    cmp dword [condition_sp], 0
    jz parse_error
    
    ; Get the last condition result
    dec dword [condition_sp]
    mov ebx, [condition_sp]
    mov al, [condition_stack + ebx]
    
    ; Invert the condition
    xor al, 1
    
    pop esi
    ret

; Parse condition (simple comparison for now)
parse_condition:
    push esi
    
    ; Get first operand
    call parse_number
    push eax
    
    ; Skip whitespace
    inc esi
    
    ; Get comparison operator
    mov al, [esi]
    inc esi
    
    ; Skip whitespace
    inc esi
    
    ; Get second operand
    call parse_number
    mov ebx, eax
    pop eax
    
    ; Compare values
    cmp eax, ebx
    mov al, 0
    setg al      ; Set al to 1 if first > second
    
    pop esi
    ret

; Loop handling is already defined above

; Handle loop statement
handle_loop:
    push esi
    inc esi                    ; Skip space after keyword

    ; Parse loop count
    call parse_number
    
    ; Store loop count
    mov ebx, [loop_count]
    mov [loop_stack + ebx * 4], eax
    inc dword [loop_count]

    ; Execute loop body
loop_body:
    push eax                   ; Save counter
    call parse_command         ; Parse and execute loop body
    pop eax                   ; Restore counter
    dec eax
    jnz loop_body

    ; End of loop
    dec dword [loop_count]
    
    pop esi
    ret

; Function handling is already defined above

; Handle function definition
handle_function:
    push esi
    inc esi                    ; Skip space after keyword

    ; Store function name
    mov edi, [func_count]
    imul edi, 16              ; Each entry is 16 bytes (name + entry point)
    add edi, func_table

func_name_loop:
    mov al, [esi]
    cmp al, ' '
    je func_name_done
    mov [edi], al
    inc edi
    inc esi
    jmp func_name_loop

func_name_done:
    mov byte [edi], 0         ; Null terminate
    inc esi                   ; Skip space

    ; Store function entry point
    mov eax, esi
    mov [edi + 8], eax        ; Store entry point

    ; Increment function count
    inc dword [func_count]

    ; Skip function body for now
    call skip_function_body

    pop esi
    ret

; Skip function body (until end of line)
skip_function_body:
    push esi
skip_loop:
    mov al, [esi]
    cmp al, 0xA              ; Newline
    je skip_done
    inc esi
    jmp skip_loop
skip_done:
    pop esi
    ret

; Call function by name
call_function:
    push esi
    
    ; Check stack depth
    mov eax, [current_stack_depth]
    cmp eax, max_stack_depth
    jge stack_overflow
    
    ; Increment stack depth
    inc dword [current_stack_depth]
    
    ; Find function in table
    mov ecx, [func_count]
    test ecx, ecx
    jz func_not_found
    mov edi, func_table

find_func:
    push ecx
    push esi
    call compare_string
    pop esi
    pop ecx
    test eax, eax
    jnz execute_function
    add edi, 16              ; Move to next function entry
    loop find_func
    jmp func_not_found

execute_function:
    ; Save return address on call stack
    mov eax, [current_stack_depth]
    dec eax
    mov ebx, esi            ; Save current position
    mov [call_stack + eax * 4], ebx
    
    mov esi, [edi + 8]       ; Get function entry point
    call parse_command       ; Execute function body
    
    ; Restore return address
    mov eax, [current_stack_depth]
    dec eax
    mov esi, [call_stack + eax * 4]
    
    ; Decrement stack depth
    dec dword [current_stack_depth]
    
    pop esi
    ret

stack_overflow:
    mov eax, 4
    mov ebx, 1
    mov ecx, stack_overflow_error
    mov edx, stack_overflow_len
    int 0x80
    pop esi
    ret

func_not_found:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_msg
    mov edx, error_len
    int 0x80
    pop esi
    ret

check_concurrency:
    mov edi, kw_thread
    call compare_string
    test eax, eax
    jnz handle_thread
    
    mov edi, kw_thread_hi
    call compare_string
    test eax, eax
    jnz handle_thread

    mov edi, kw_mutex
    call compare_string
    test eax, eax
    jnz handle_mutex
    
    mov edi, kw_mutex_hi
    call compare_string
    test eax, eax
    jnz handle_mutex

    mov edi, kw_async
    call compare_string
    test eax, eax
    jnz handle_async
    
    mov edi, kw_async_hi
    call compare_string
    test eax, eax
    jnz handle_async

    jmp check_cloud
    jmp parse_done

; Thread handling implementation
handle_thread:
    push ebp
    mov ebp, esp
    
    ; Call thread creation from समकालिकता.asm
    call create_thread
    test eax, eax
    jz thread_error_handler
    
    mov esp, ebp
    pop ebp
    ret

; Mutex handling implementation
handle_mutex:
    push ebp
    mov ebp, esp
    
    ; Call mutex creation from समकालिकता.asm
    call create_mutex
    test eax, eax
    jz mutex_error_handler
    
    mov esp, ebp
    pop ebp
    ret

; Async operation handling
handle_async:
    push ebp
    mov ebp, esp
    
    ; Set up async context
    call setup_async_context
    
    ; Queue the async operation
    call queue_async_operation
    
    mov esp, ebp
    pop ebp
    ret

; Error handlers for concurrency operations
thread_error_handler:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_thread
    mov edx, error_thread_len
    int 0x80
    ret

mutex_error_handler:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_mutex
    mov edx, error_mutex_len
    int 0x80
    ret
    jmp check_cloud
    jmp parse_done

check_debug:
    ; Check for debugger commands
    mov edi, kw_debug
    call compare_string
    test eax, eax
    jnz handle_debug
    
    mov edi, kw_debug_hi
    call compare_string
    test eax, eax
    jnz handle_debug

    mov edi, kw_break
    call compare_string
    test eax, eax
    jnz handle_breakpoint
    
    mov edi, kw_break_hi
    call compare_string
    test eax, eax
    jnz handle_breakpoint

    mov edi, kw_inspect
    call compare_string
    test eax, eax
    jnz handle_inspect
    
    mov edi, kw_inspect_hi
    call compare_string
    test eax, eax
    jnz handle_inspect

    jmp check_cloud
    jmp parse_done

handle_debug:
    call init_debugger
    test eax, eax
    jz parse_error
    jmp parse_done

handle_breakpoint:
    ; Get breakpoint address
    call skip_whitespace
    call parse_number
    push eax
    call set_breakpoint
    add esp, 4
    test eax, eax
    jz parse_error
    jmp parse_done

handle_inspect:
    ; Get variable name
    call skip_whitespace
    push esi
    call inspect_variable
    add esp, 4
    test eax, eax
    jz parse_error
    jmp parse_done
    jmp parse_done

check_nn:
    ; Check for type system operations
    mov edi, kw_type
    call compare_string
    test eax, eax
    jnz handle_type_definition
    
    mov edi, kw_type_hi
    call compare_string
    test eax, eax
    jnz handle_type_definition

    mov edi, kw_interface
    call compare_string
    test eax, eax
    jnz handle_interface
    
    mov edi, kw_interface_hi
    call compare_string
    test eax, eax
    jnz handle_interface

    mov edi, kw_generic
    call compare_string
    test eax, eax
    jnz handle_generic
    
    mov edi, kw_generic_hi
    call compare_string
    test eax, eax
    jnz handle_generic

    jmp check_cloud

; Handle type definition
handle_type_definition:
    call parse_type_definition
    test eax, eax
    jz parse_error
    jmp parse_done

; Handle interface definition
handle_interface:
    call parse_interface
    test eax, eax
    jz parse_error
    jmp parse_done

; Handle generic type
handle_generic:
    call parse_generic
    test eax, eax
    jz parse_error
    jmp parse_done

    jmp parse_done
    pop esi
    ret

; String comparison function
compare_string:
    push ebx
compare_loop:
    mov bl, [esi]
    mov bh, [edi]
    cmp bl, bh
    jne compare_end
    test bl, bl
    jz compare_match
    inc esi
    inc edi
    jmp compare_loop
compare_match:
    mov eax, 1
compare_end:
    pop ebx
    ret

; Handle variable declaration and assignment
handle_variable:
    push esi
    inc esi                    ; Skip space after keyword

    ; Parse variable name
    mov edi, [var_count]
    imul edi, 8               ; Each entry is 8 bytes (name + value)
    add edi, var_table

var_name_loop:
    mov al, [esi]
    cmp al, ' '
    je var_name_done
    cmp al, '='
    je var_name_done
    mov [edi], al
    inc edi
    inc esi
    jmp var_name_loop

var_name_done:
    mov byte [edi], 0        ; Null terminate

    ; Skip to value
find_equals:
    cmp byte [esi], '='
    je parse_value
    inc esi
    jmp find_equals

parse_value:
    inc esi                  ; Skip = sign
    call parse_number
    mov [edi + 4], eax       ; Store value

    ; Increment variable count
    inc dword [var_count]

    pop esi
    ret

; Parse number from string
parse_number:
    xor eax, eax             ; Clear result
parse_digit:
    movzx ecx, byte [esi]    ; Get current digit
    sub ecx, '०'             ; Convert from Devanagari numeral
    jb parse_done            ; Exit if not a digit
    cmp ecx, 9
    ja parse_done
    imul eax, 10             ; Multiply current result by 10
    add eax, ecx             ; Add new digit
    inc esi
    jmp parse_digit
parse_done:
    ret

; Handle print command
handle_print:
    push esi
    inc esi                  ; Skip opening parenthesis

    ; Find variable in table
    mov ecx, [var_count]
    test ecx, ecx
    jz print_error
    mov edi, var_table

find_var:
    push ecx
    push esi
    call compare_string
    pop esi
    pop ecx
    test eax, eax
    jnz print_value
    add edi, 8               ; Move to next variable entry
    loop find_var
    jmp print_error

print_value:
    mov eax, [edi + 4]       ; Get variable value
    push eax
    call print_number
    mov eax, 4               ; Print newline
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80
    pop eax
    pop esi
    ret

print_error:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_msg
    mov edx, error_len
    int 0x80
    pop esi
    ret

; Print a number
print_number:
    push ebp
    mov ebp, esp
    sub esp, 16              ; Local buffer for digits
    mov eax, [ebp + 8]       ; Get number to print
    mov edi, esp
    add edi, 15              ; Start from end of buffer
    mov byte [edi], 0        ; Null terminate

convert_loop:
    dec edi
    mov edx, 0
    mov ecx, 10
    div ecx
    add dl, '०'              ; Convert to Devanagari numeral
    mov [edi], dl
    test eax, eax
    jnz convert_loop

    ; Print the number
    mov eax, 4
    mov ebx, 1
    mov ecx, edi
    mov edx, esp
    add edx, 15
    sub edx, edi
    int 0x80

    mov esp, ebp
    pop ebp
    ret 4

exit_program:
    mov eax, 1          ; sys_exit
    xor ebx, ebx        ; return 0
    int 0x80

; Removed duplicate check_if and check_else blocks

; Handle if statement
handle_if:
    push esi
    inc esi                    ; Skip space after keyword

    ; Parse condition
    call parse_condition
    
    ; Store condition result
    mov ebx, [condition_sp]
    mov [condition_stack + ebx], al
    inc dword [condition_sp]

    pop esi
    ret

; Handle else statement
handle_else:
    push esi
    
    ; Check if we have a matching if
    cmp dword [condition_sp], 0
    jz parse_error
    
    ; Get the last condition result
    dec dword [condition_sp]
    mov ebx, [condition_sp]
    mov al, [condition_stack + ebx]
    
    ; Invert the condition
    xor al, 1
    
    pop esi
    ret

; Parse condition (simple comparison for now)
parse_condition:
    push esi
    
    ; Get first operand
    call parse_number
    push eax
    
    ; Skip whitespace
    inc esi
    
    ; Get comparison operator
    mov al, [esi]
    inc esi
    
    ; Skip whitespace
    inc esi
    
    ; Get second operand
    call parse_number
    mov ebx, eax
    pop eax
    
    ; Compare values
    cmp eax, ebx
    mov al, 0
    setg al      ; Set al to 1 if first > second
    
    pop esi
    ret

; Loop handling is already defined above

; Handle loop statement
handle_loop:
    push esi
    inc esi                    ; Skip space after keyword

    ; Parse loop count
    call parse_number
    
    ; Store loop count
    mov ebx, [loop_count]
    mov [loop_stack + ebx * 4], eax
    inc dword [loop_count]

    ; Execute loop body
loop_body:
    push eax                   ; Save counter
    call parse_command         ; Parse and execute loop body
    pop eax                   ; Restore counter
    dec eax
    jnz loop_body

    ; End of loop
    dec dword [loop_count]
    
    pop esi
    ret

; Function handling is already defined above

; Handle function definition
handle_function:
    push esi
    inc esi                    ; Skip space after keyword

    ; Store function name
    mov edi, [func_count]
    imul edi, 16              ; Each entry is 16 bytes (name + entry point)
    add edi, func_table

func_name_loop:
    mov al, [esi]
    cmp al, ' '
    je func_name_done
    mov [edi], al
    inc edi
    inc esi
    jmp func_name_loop

func_name_done:
    mov byte [edi], 0         ; Null terminate
    inc esi                   ; Skip space

    ; Store function entry point
    mov eax, esi
    mov [edi + 8], eax        ; Store entry point

    ; Increment function count
    inc dword [func_count]

    ; Skip function body for now
    call skip_function_body

    pop esi
    ret

; Skip function body (until end of line)
skip_function_body:
    push esi
skip_loop:
    mov al, [esi]
    cmp al, 0xA              ; Newline
    je skip_done
    inc esi
    jmp skip_loop
skip_done:
    pop esi
    ret

; Call function by name
call_function:
    push esi
    
    ; Check stack depth
    mov eax, [current_stack_depth]
    cmp eax, max_stack_depth
    jge stack_overflow
    
    ; Increment stack depth
    inc dword [current_stack_depth]
    
    ; Find function in table
    mov ecx, [func_count]
    test ecx, ecx
    jz func_not_found
    mov edi, func_table

find_func:
    push ecx
    push esi
    call compare_string
    pop esi
    pop ecx
    test eax, eax
    jnz execute_function
    add edi, 16              ; Move to next function entry
    loop find_func
    jmp func_not_found

execute_function:
    ; Save return address on call stack
    mov eax, [current_stack_depth]
    dec eax
    mov ebx, esi            ; Save current position
    mov [call_stack + eax * 4], ebx
    
    mov esi, [edi + 8]       ; Get function entry point
    call parse_command       ; Execute function body
    
    ; Restore return address
    mov eax, [current_stack_depth]
    dec eax
    mov esi, [call_stack + eax * 4]
    
    ; Decrement stack depth
    dec dword [current_stack_depth]
    
    pop esi
    ret

stack_overflow:
    mov eax, 4
    mov ebx, 1
    mov ecx, stack_overflow_error
    mov edx, stack_overflow_len
    int 0x80
    pop esi
    ret

func_not_found:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_msg
    mov edx, error_len
    int 0x80
    pop esi
    ret

check_concurrency:
    mov edi, kw_thread
    call compare_string
    test eax, eax
    jnz handle_thread
    
    mov edi, kw_thread_hi
    call compare_string
    test eax, eax
    jnz handle_thread

    mov edi, kw_mutex
    call compare_string
    test eax, eax
    jnz handle_mutex
    
    mov edi, kw_mutex_hi
    call compare_string
    test eax, eax
    jnz handle_mutex

    mov edi, kw_async
    call compare_string
    test eax, eax
    jnz handle_async
    
    mov edi, kw_async_hi
    call compare_string
    test eax, eax
    jnz handle_async

    jmp check_cloud
    jmp parse_done

; Thread handling implementation
handle_thread:
    push ebp
    mov ebp, esp
    
    ; Call thread creation from समकालिकता.asm
    call create_thread
    test eax, eax
    jz thread_error_handler
    
    mov esp, ebp
    pop ebp
    ret

; Mutex handling implementation
handle_mutex:
    push ebp
    mov ebp, esp
    
    ; Call mutex creation from समकालिकता.asm
    call create_mutex
    test eax, eax
    jz mutex_error_handler
    
    mov esp, ebp
    pop ebp
    ret

; Async operation handling
handle_async:
    push ebp
    mov ebp, esp
    
    ; Set up async context
    call setup_async_context
    
    ; Queue the async operation
    call queue_async_operation
    
    mov esp, ebp
    pop ebp
    ret

; Error handlers for concurrency operations
thread_error_handler:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_thread
    mov edx, error_thread_len
    int 0x80
    ret

mutex_error_handler:
    mov eax, 4
    mov ebx, 1
    mov ecx, error_mutex
    mov edx, error_mutex_len
    int 0x80
    ret
    jmp check_cloud
    jmp parse_done

check_debug:
    ; Check for debugger commands
    mov edi, kw_debug
    call compare_string
    test eax, eax
    jnz handle_debug
    
    mov edi, kw_debug_hi
    call compare_string
    test eax, eax
    jnz handle_debug

    mov edi, kw_break
    call compare_string
    test eax, eax
    jnz handle_breakpoint
    
    mov edi, kw_break_hi
    call compare_string
    test eax, eax
    jnz handle_breakpoint

    mov edi, kw_inspect
    call compare_string
    test eax, eax
    jnz handle_inspect
    
    mov edi, kw_inspect_hi
    call compare_string
    test eax, eax
    jnz handle_inspect

    jmp check_cloud
    jmp parse_done

handle_debug:
    call init_debugger
    test eax, eax
    jz parse_error
    jmp parse_done

handle_breakpoint:
    ; Get breakpoint address
    call skip_whitespace
    call parse_number
    push eax
    call set_breakpoint
    add esp, 4
    test eax, eax
    jz parse_error
    jmp parse_done

handle_inspect:
    ; Get variable name
    call skip_whitespace
    push esi
    call inspect_variable
    add esp, 4
    test eax, eax
    jz parse_error
    jmp parse_done
    jmp parse_done