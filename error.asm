section .data
    ; Error type definitions
    error_type_error db 'त्रुटि', 0    ; Error type
    error_type_warning db 'चेतावनी', 0  ; Warning type

    ; Common error messages
    syntax_error db 'वाक्यरचना त्रुटि: ', 0
    type_error db 'प्रकार त्रुटि: ', 0
    stack_error db 'स्टैक त्रुटि: ', 0
    memory_error db 'स्मृति त्रुटि: ', 0
    runtime_error db 'रनटाइम त्रुटि: ', 0

    ; Warning messages
    deprecated_warning db 'अवमूल्यन चेतावनी: ', 0
    performance_warning db 'प्रदर्शन चेतावनी: ', 0

    ; Error formatting
    at_line db ' पंक्ति ', 0
    at_char db ' अक्षर ', 0
    newline db 0xA

section .text
    global error_init
    global raise_error
    global raise_warning

; Initialize error handling
error_init:
    ret

; Raise an error with message
; Parameters: error_type (ebp+8), message (ebp+12), line (ebp+16), char (ebp+20)
raise_error:
    push ebp
    mov ebp, esp

    ; Print error type
    mov eax, 4
    mov ebx, 1
    mov ecx, [ebp + 8]
    mov edx, 0              ; Calculate string length
    call strlen
    int 0x80

    ; Print error message
    mov eax, 4
    mov ebx, 1
    mov ecx, [ebp + 12]
    mov edx, 0              ; Calculate string length
    call strlen
    int 0x80

    ; Print line information
    mov eax, 4
    mov ebx, 1
    mov ecx, at_line
    mov edx, 8
    int 0x80

    ; Print line number
    mov eax, [ebp + 16]
    call print_number

    ; Print character information
    mov eax, 4
    mov ebx, 1
    mov ecx, at_char
    mov edx, 7
    int 0x80

    ; Print character position
    mov eax, [ebp + 20]
    call print_number

    ; Print newline
    mov eax, 4
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80

    mov esp, ebp
    pop ebp
    ret 16

; Raise a warning with message
; Parameters: warning_type (ebp+8), message (ebp+12)
raise_warning:
    push ebp
    mov ebp, esp

    ; Print warning type
    mov eax, 4
    mov ebx, 1
    mov ecx, [ebp + 8]
    mov edx, 0              ; Calculate string length
    call strlen
    int 0x80

    ; Print warning message
    mov eax, 4
    mov ebx, 1
    mov ecx, [ebp + 12]
    mov edx, 0              ; Calculate string length
    call strlen
    int 0x80

    ; Print newline
    mov eax, 4
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80

    mov esp, ebp
    pop ebp
    ret 8

; Calculate string length
; Parameter: string address in ecx
; Returns: length in edx
strlen:
    push ecx
    xor edx, edx
strlen_loop:
    cmp byte [ecx], 0
    je strlen_done
    inc edx
    inc ecx
    jmp strlen_loop
strlen_done:
    pop ecx
    ret

; Print a number (reused from repl.asm)
print_number:
    push ebp
    mov ebp, esp
    sub esp, 16              ; Local buffer for digits
    mov eax, [ebp + 8]       ; Get number to print
    mov edi, esp
    add edi, 15              ; Start from end of buffer
    mov byte [edi], 0        ; Null terminate

convert_loop:
    dec edi
    mov edx, 0
    mov ecx, 10
    div ecx
    add dl, '०'              ; Convert to Devanagari numeral
    mov [edi], dl
    test eax, eax
    jnz convert_loop

    ; Print the number
    mov eax, 4
    mov ebx, 1
    mov ecx, edi
    mov edx, esp
    add edx, 15
    sub edx, edi
    int 0x80

    mov esp, ebp
    pop ebp
    ret 4