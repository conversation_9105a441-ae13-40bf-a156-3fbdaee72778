section .data
    ; Advanced mathematical operation keywords in Sanskrit
    kw_power db 'घात', 0            ; Power/Exponent
    kw_log db 'लघुगणक', 0           ; Logarithm
    kw_sin db 'ज्या', 0              ; Sine
    kw_cos db 'कोज्या', 0            ; Cosine
    kw_tan db 'स्पर्शज्या', 0        ; Tangent
    kw_factorial db 'क्रमगुणन', 0     ; Factorial
    
    ; Hindi keywords for mathematical operations
    kw_power_hi db 'घात', 0          ; Power in Hindi
    kw_log_hi db 'लॉग', 0            ; Log in Hindi
    kw_sin_hi db 'साइन', 0           ; Sine in Hindi
    kw_cos_hi db 'कोसाइन', 0         ; Cosine in Hindi
    kw_tan_hi db 'टैन', 0            ; Tan in Hindi
    kw_factorial_hi db 'क्रमगुणन', 0   ; Factorial in Hindi
    
    ; Constants
    pi dd 3.14159265359
    e dd 2.71828182846
    
    ; Error messages
    error_domain db 'त्रुटि: Invalid domain for operation', 0xA
    error_domain_len equ $ - error_domain

section .text
    global power
    global logarithm
    global sine
    global cosine
    global tangent
    global factorial

; Power function (घात/power)
; Parameters: eax (base), ebx (exponent)
; Returns: eax = result
power:
    push ecx
    mov ecx, eax        ; Save base
    dec ebx             ; Decrement exponent for loop
    
power_loop:
    test ebx, ebx
    jz power_done
    imul eax, ecx       ; Multiply by base
    dec ebx
    jmp power_loop
    
power_done:
    pop ecx
    ret

; Factorial function (क्रमगुणन/factorial)
; Parameters: eax
; Returns: eax = result
factorial:
    push ebx
    push ecx
    
    mov ebx, eax        ; Save input
    mov ecx, eax        ; Counter
    dec ecx             ; Start multiplying from n-1
    
fact_loop:
    test ecx, ecx
    jz fact_done
    imul eax, ecx       ; Multiply by counter
    dec ecx
    jmp fact_loop
    
fact_done:
    pop ecx
    pop ebx
    ret

; Logarithm approximation (लघुगणक/logarithm)
; Uses Taylor series approximation
; Parameters: eax (number)
; Returns: eax = floor(ln(number))
logarithm:
    push ebx
    push ecx
    push edx
    
    ; Implementation using Taylor series
    ; ln(x) = 2(a + (a^3)/3 + (a^5)/5 ...)
    ; where a = (x-1)/(x+1)
    
    ; Basic implementation for positive integers
    mov ecx, 0          ; Result counter
    
log_loop:
    cmp eax, 1
    jle log_done
    shr eax, 1          ; Divide by 2
    inc ecx             ; Count divisions
    jmp log_loop
    
log_done:
    mov eax, ecx
    pop edx
    pop ecx
    pop ebx
    ret