{"name": "sanskrit-mobile", "version": "1.0.0", "description": "Mobile app for Sanskrit Programming Language", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "^48.0.0", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.8", "@react-native-async-storage/async-storage": "1.17.11", "react-native-code-editor": "^1.3.3", "react-native-webview": "11.26.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.0.14", "typescript": "^4.9.4"}, "private": true}