टिप्पणी संस्कृत से LLVM IR जनरेटर

टिप्पणी LLVM IR प्रकार परिभाषाएं
परिवर्तनीय llvm_प्रकार_पूर्णांक = "i32"
परिवर्तनीय llvm_प्रकार_वास्तविक = "double"
परिवर्तनीय llvm_प्रकार_बूलियन = "i1"
परिवर्तनीय llvm_प्रकार_शून्य = "void"

टिप्पणी LLVM IR निर्देश टेम्पलेट
परिवर्तनीय llvm_जोड़ = "add {प्रकार} {परिणाम}, {ऑप1}, {ऑप2}"
परिवर्तनीय llvm_घटा = "sub {प्रकार} {परिणाम}, {ऑप1}, {ऑप2}"
परिवर्तनीय llvm_गुणा = "mul {प्रकार} {परिणाम}, {ऑप1}, {ऑप2}"
परिवर्तनीय llvm_भाग = "sdiv {प्रकार} {परिणाम}, {ऑप1}, {ऑप2}"

टिप्पणी LLVM IR जनरेशन कार्य
कार्य llvm_कोड_जनरेट(एएसटी) {
    परिवर्तनीय llvm_कोड = "; LLVM IR जनरेट किया गया संस्कृत से\n"
    llvm_कोड += "target datalayout = \"e-m:e-p270:32:32-p271:32:32-p272:64:64-i64:64-f80:128-n8:16:32:64-S128\"\n"
    llvm_कोड += "target triple = \"x86_64-pc-linux-gnu\"\n\n"
    
    टिप्पणी मॉड्यूल स्तर घोषणाएं जनरेट करें
    एएसटी.बच्चे.प्रत्येक(बच्चा => {
        llvm_कोड += llvm_घोषणा_जनरेट(बच्चा)
    })
    
    llvm_कोड
}

टिप्पणी LLVM IR घोषणा जनरेशन
कार्य llvm_घोषणा_जनरेट(नोड) {
    यदि (नोड.प्रकार == नोड_कार्य) {
        परिवर्तनीय फंक_घोषणा = "define {लौटाना_प्रकार} @{नाम}({पैरामीटर}) {\n"
        फंक_घोषणा = फंक_घोषणा.प्रतिस्थापन({
            लौटाना_प्रकार: llvm_प्रकार_मैप(नोड.लौटाना_प्रकार),
            नाम: नोड.नाम,
            पैरामीटर: llvm_पैरामीटर_जनरेट(नोड.पैरामीटर)
        })
        
        टिप्पणी फंक्शन बॉडी जनरेट करें
        नोड.शरीर.प्रत्येक(कथन => {
            फंक_घोषणा += "    " + llvm_कथन_जनरेट(कथन) + "\n"
        })
        
        फंक_घोषणा += "}\n"
        फंक_घोषणा
    }
}

टिप्पणी सहायक कार्य
कार्य llvm_प्रकार_मैप(संस्कृत_प्रकार) {
    यदि (संस्कृत_प्रकार == "पूर्णांक") {
        llvm_प्रकार_पूर्णांक
    } अन्यथा यदि (संस्कृत_प्रकार == "वास्तविक") {
        llvm_प्रकार_वास्तविक
    } अन्यथा यदि (संस्कृत_प्रकार == "बूलियन") {
        llvm_प्रकार_बूलियन
    } अन्यथा {
        llvm_प्रकार_शून्य
    }
}

कार्य llvm_पैरामीटर_जनरेट(पैरामीटर_सूची) {
    पैरामीटर_सूची.मैप(पैरा => {
        llvm_प्रकार_मैप(पैरा.प्रकार) + " %" + पैरा.नाम
    }).जोड़ें(", ")
}

कार्य llvm_कथन_जनरेट(कथन) {
    यदि (कथन.प्रकार == "वापसी") {
        "ret " + llvm_प्रकार_मैप(कथन.लौटाना_प्रकार) + " " + कथन.मान
    } अन्यथा यदि (कथन.प्रकार == "आरोपण") {
        "store " + llvm_प्रकार_मैप(कथन.प्रकार) + " " + कथन.मान + ", " +
        llvm_प्रकार_मैप(कथन.प्रकार) + "* %" + कथन.लक्ष्य
    }
}