टिप्पणी मानक पुस्तकालय कार्यान्वयन

टिप्पणी सूची डेटा संरचना
कार्य सूची() {
    परिवर्तनीय सूची = {
        तत्व: [],
        लंबाई: ०,
        
        जोड़ें: कार्य(मान) {
            यह.तत्व[यह.लंबाई] = मान
            यह.लंबाई = यह.लंबाई + १
        },
        
        हटाएं: कार्य() {
            यदि (यह.लंबाई > ०) {
                यह.लंबाई = यह.लंबाई - १
                यह.तत्व[यह.लंबाई]
            }
        },
        
        प्राप्त: कार्य(सूचकांक) {
            यदि (सूचकांक >= ० && सूचकांक < यह.लंबाई) {
                यह.तत्व[सूचकांक]
            }
        }
    }
    सूची
}

टिप्पणी वाक्य संचालन
कार्य वाक्य_जोड़ें(वाक्य१, वाक्य२) {
    वाक्य१ + वाक्य२
}

कार्य वाक्य_विभाजन(वाक्य, विभाजक) {
    परिवर्तनीय परिणाम = सूची()
    परिवर्तनीय वर्तमान = ""
    
    पुनरावर्तनम् (वर्ण इन वाक्य) {
        यदि (वर्ण == विभाजक) {
            यदि (वर्तमान.लंबाई() > ०) {
                परिणाम.जोड़ें(वर्तमान)
                वर्तमान = ""
            }
        } अन्यथा {
            वर्तमान = वाक्य_जोड़ें(वर्तमान, वर्ण)
        }
    }
    
    यदि (वर्तमान.लंबाई() > ०) {
        परिणाम.जोड़ें(वर्तमान)
    }
    
    परिणाम
}

टिप्पणी अतिरिक्त वाक्य कार्य
कार्य वाक्य_खोज(वाक्य, खोज) {
    परिवर्तनीय स्थिति = -१
    परिवर्तनीय इ = ०
    
    पुनरावर्तनम् (इ < वाक्य.लंबाई() - खोज.लंबाई() + १) {
        यदि (वाक्य.उपवाक्य(इ, इ + खोज.लंबाई()) == खोज) {
            स्थिति = इ
            विराम
        }
        इ = इ + १
    }
    
    स्थिति
}

कार्य वाक्य_बदलें(वाक्य, पुराना, नया) {
    परिवर्तनीय परिणाम = ""
    परिवर्तनीय इ = ०
    
    पुनरावर्तनम् (इ < वाक्य.लंबाई()) {
        यदि (वाक्य.उपवाक्य(इ, इ + पुराना.लंबाई()) == पुराना) {
            परिणाम = वाक्य_जोड़ें(परिणाम, नया)
            इ = इ + पुराना.लंबाई()
        } अन्यथा {
            परिणाम = वाक्य_जोड़ें(परिणाम, वाक्य[इ])
            इ = इ + १
        }
    }
    
    परिणाम
}

टिप्पणी गणित कार्य
कार्य अधिकतम(क, ख) {
    यदि (क > ख) {
        क
    } अन्यथा {
        ख
    }
}

कार्य न्यूनतम(क, ख) {
    यदि (क < ख) {
        क
    } अन्यथा {
        ख
    }
}

कार्य पूर्णांक(संख्या) {
    संख्या - (संख्या % १)
}

टिप्पणी उन्नत गणित कार्य
कार्य वर्ग(संख्या) {
    संख्या * संख्या
}

कार्य घन(संख्या) {
    संख्या * संख्या * संख्या
}

कार्य वर्गमूल(संख्या) {
    यदि (संख्या < ०) {
        वापस -१
    }
    
    परिवर्तनीय अनुमान = संख्या / २
    परिवर्तनीय पुनरावृत्ति = १०
    
    पुनरावर्तनम् (पुनरावृत्ति > ०) {
        अनुमान = (अनुमान + संख्या / अनुमान) / २
        पुनरावृत्ति = पुनरावृत्ति - १
    }
    
    अनुमान
}

कार्य शेषफल(क, ख) {
    क % ख
}

कार्य औसत(...संख्याएं) {
    परिवर्तनीय योग = ०
    परिवर्तनीय गणना = ०
    
    पुनरावर्तनम् (संख्या इन संख्याएं) {
        योग = योग + संख्या
        गणना = गणना + १
    }
    
    यदि (गणना > ०) {
        योग / गणना
    } अन्यथा {
        ०
    }
}