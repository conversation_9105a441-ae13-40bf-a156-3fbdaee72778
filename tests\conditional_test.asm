; Test file for conditional branching in Sanskrit
section .data
    test_header db 'Running Conditional Tests', 0xA
    test_header_len equ $ - test_header
    test_pass db 'Test Passed: ', 0
    test_pass_len equ $ - test_pass
    test_fail db 'Test Failed: ', 0
    test_fail_len equ $ - test_fail
    newline db 0xA

    ; Test inputs
    test_if_true db 'यदि ५ > ३:', 0
    test_if_false db 'यदि २ > ४:', 0
    test_else db 'अन्यथा:', 0

    ; Expected results
    expected_true equ 1
    expected_false equ 0

section .text
    global _start

_start:
    ; Print test header
    mov eax, 4
    mov ebx, 1
    mov ecx, test_header
    mov edx, test_header_len
    int 0x80

    ; Test 1: True condition
    mov esi, test_if_true
    call parse_command
    mov eax, [condition_result]
    cmp eax, expected_true
    jne test1_fail

    ; Test 2: False condition
    mov esi, test_if_false
    call parse_command
    mov eax, [condition_result]
    cmp eax, expected_false
    jne test2_fail

    ; All tests passed
    mov eax, 4
    mov ebx, 1
    mov ecx, test_pass
    mov edx, test_pass_len
    int 0x80
    jmp exit_program

test1_fail:
    mov eax, 4
    mov ebx, 1
    mov ecx, test_fail
    mov edx, test_fail_len
    int 0x80
    mov ecx, test_if_true
    call print_string
    jmp exit_program

test2_fail:
    mov eax, 4
    mov ebx, 1
    mov ecx, test_fail
    mov edx, test_fail_len
    int 0x80
    mov ecx, test_if_false
    call print_string
    jmp exit_program

exit_program:
    mov eax, 1
    xor ebx, ebx
    int 0x80

; Helper function to print null-terminated string
print_string:
    push ecx
    mov edx, 0
count_loop:
    cmp byte [ecx + edx], 0
    je print_now
    inc edx
    jmp count_loop
print_now:
    mov eax, 4
    mov ebx, 1
    pop ecx
    int 0x80
    ret

section .bss
    condition_result resb 4