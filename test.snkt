// Test 1: Basic variable declarations and conditional statements
परिवर्तनीय अ = १०
परिवर्तनीय ब = २०

यदि अ < ब:
    लेखय("अ लघु अस्ति")
अन्यथा:
    लेखय("ब लघु अस्ति")

// Test 2: Arithmetic operations
परिवर्तनीय योग = अ + ब
परिवर्तनीय अंतर = ब - अ
परिवर्तनीय गुणन = अ * ब
परिवर्तनीय भाग = ब / अ

लेखय("गणित परिणामः")
लेखय(योग)
लेखय(अंतर)
लेखय(गुणन)
लेखय(भाग)

// Test 3: Loop demonstration
परिवर्तनीय गणक = १
पुनरावर्तनम् गणक <= ५:
    लेखय(गणक)
    गणक = गणक + १

// Test 4: Array operations
सूचि फलानि = ["आम्र", "नारङ्ग", "द्राक्षा"]
लेखय(फलानि[०])

// Test 5: Function definition and call
कार्य नमस्कार(नाम):
    लेखय("नमस्ते " + नाम)

नमस्कार("जगत्")

लेखय("कार्य समाप्तम्")