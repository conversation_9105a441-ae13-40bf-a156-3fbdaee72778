<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sanskrit Programming Language Playground</title>
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5/lib/codemirror.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5/theme/monokai.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .playground {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }
        .editor-section, .output-section {
            display: flex;
            flex-direction: column;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .CodeMirror {
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .output {
            height: 500px;
            background-color: #2d2d2d;
            color: #fff;
            padding: 10px;
            border-radius: 4px;
            overflow-y: auto;
            font-family: monospace;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .variable-panel {
            position: fixed;
            right: 20px;
            top: 100px;
            width: 200px;
            background: #2d2d2d;
            color: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .variable-panel h4 {
            margin-top: 0;
            border-bottom: 1px solid #555;
            padding-bottom: 5px;
        }
        .debug-controls {
            margin-top: 10px;
            display: flex;
            gap: 5px;
        }
        .breakpoint {
            color: #822;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>संस्कृत Programming Language Playground</h1>
        </div>
        <div class="playground">
            <div class="editor-section">
                <div class="section-header">
                    <h3>Code Editor</h3>
                    <div>
                        <input type="file" id="fileInput" accept=".snkt" style="display: none">
                        <button id="uploadBtn">Upload .snkt</button>
                        <button id="runBtn">Run</button>
                        <button id="debugBtn">Debug</button>
                        <button id="exportBtn">Export .snkt</button>
                    </div>
                </div>
                <textarea id="editor"></textarea>
            </div>
            <div class="output-section">
                <div class="section-header">
                    <h3>Output</h3>
                    <button id="clearBtn">Clear</button>
                </div>
                <div id="output" class="output"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/codemirror@5/lib/codemirror.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5/mode/javascript/javascript.js"></script>
    <script src="app.js"></script>
</body>
</html>