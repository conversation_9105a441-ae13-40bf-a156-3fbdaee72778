const path = require('path');
const { workspace, ExtensionContext } = require('vscode');
const { LanguageClient, TransportKind } = require('vscode-languageclient/node');

let client;

function activate(context) {
  // Server options - start the server
  const serverModule = context.asAbsolutePath(path.join('server', 'server.js'));
  const serverOptions = {
    run: {
      module: serverModule,
      transport: TransportKind.ipc
    },
    debug: {
      module: serverModule,
      transport: TransportKind.ipc,
      options: { execArgv: ['--nolazy', '--inspect=6009'] }
    }
  };

  // Client options
  const clientOptions = {
    documentSelector: [{ scheme: 'file', language: 'sanskrit' }],
    synchronize: {
      fileEvents: workspace.createFileSystemWatcher('**/*.snkt')
    }
  };

  // Create and start the client
  client = new LanguageClient(
    'sanskritLanguageServer',
    'Sanskrit Language Server',
    serverOptions,
    clientOptions
  );

  client.start();
}

function deactivate() {
  if (!client) {
    return undefined;
  }
  return client.stop();
}

module.exports = {
  activate,
  deactivate
};