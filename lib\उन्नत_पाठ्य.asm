; Enhanced String Processing Library for Sanskrit Programming Language
; उन्नत पाठ्य प्रक्रिया (Enhanced Text Processing)

section .data
    ; String operation keywords
    kw_regex db 'नियमित_अभिव्यक्ति', 0    ; Regular expression
    kw_replace db 'बदलें', 0               ; Replace
    kw_split db 'विभाजन', 0               ; Split
    kw_join db 'जोड़ें', 0                 ; Join
    kw_trim db 'काटें', 0                  ; Trim
    kw_upper db 'बड़े_अक्षर', 0            ; Uppercase
    kw_lower db 'छोटे_अक्षर', 0            ; Lowercase
    kw_contains db 'शामिल_है', 0          ; Contains
    kw_starts_with db 'से_शुरू', 0        ; Starts with
    kw_ends_with db 'से_समाप्त', 0         ; Ends with
    
    ; Pattern matching characters
    pattern_any db '.', 0                  ; Any character
    pattern_digit db '\\d', 0              ; Digit
    pattern_word db '\\w', 0               ; Word character
    pattern_space db '\\s', 0              ; Whitespace
    pattern_start db '^', 0                ; Start of string
    pattern_end db '$', 0                  ; End of string
    
    ; Error messages
    error_invalid_regex db 'त्रुटि: अवैध नियमित अभिव्यक्ति', 0xA
    error_string_too_long db 'त्रुटि: स्ट्रिंग बहुत लंबी', 0xA
    error_null_string db 'त्रुटि: शून्य स्ट्रिंग', 0xA

section .bss
    ; String processing buffers
    string_buffer resb 4096            ; Main string buffer
    pattern_buffer resb 1024           ; Pattern matching buffer
    result_buffer resb 4096            ; Result buffer
    temp_buffer resb 2048              ; Temporary buffer
    split_results resb 8192            ; Split operation results
    match_positions resb 1024          ; Pattern match positions
    
    ; Processing state
    current_position resd 1            ; Current processing position
    match_count resd 1                 ; Number of matches found
    string_length resd 1               ; Length of current string

section .text
    global पाठ्य_प्रारम्भ              ; string_init
    global नियमित_खोज                 ; regex_search
    global पाठ्य_बदलें                 ; string_replace
    global पाठ्य_विभाजन               ; string_split
    global पाठ्य_जोड़ें                ; string_join
    global पाठ्य_काटें                 ; string_trim
    global बड़े_अक्षर_में               ; to_uppercase
    global छोटे_अक्षर_में              ; to_lowercase
    global पाठ्य_शामिल                ; string_contains
    global पाठ्य_प्रारम्भ_जांच          ; string_starts_with
    global पाठ्य_अंत_जांच              ; string_ends_with
    global पाठ्य_लंबाई                ; string_length_func
    global पाठ्य_उपभाग                ; substring
    global पाठ्य_दोहराएं               ; string_repeat

; Initialize string processing module
पाठ्य_प्रारम्भ:
    push ebp
    mov ebp, esp
    
    ; Clear buffers
    mov edi, string_buffer
    mov ecx, 4096
    xor eax, eax
    rep stosb
    
    ; Initialize state
    mov dword [current_position], 0
    mov dword [match_count], 0
    mov dword [string_length], 0
    
    mov esp, ebp
    pop ebp
    ret

; Regular expression search
; Parameters: string, pattern
; Returns: number of matches
नियमित_खोज:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    mov esi, [ebp + 8]              ; string
    mov edi, [ebp + 12]             ; pattern
    
    ; Copy string to buffer
    mov ecx, 4096
    mov edx, string_buffer
    
.copy_string:
    lodsb
    mov [edx], al
    inc edx
    test al, al
    jz .string_copied
    loop .copy_string
    
.string_copied:
    ; Copy pattern to buffer
    mov esi, edi
    mov edi, pattern_buffer
    mov ecx, 1024
    
.copy_pattern:
    lodsb
    stosb
    test al, al
    jz .pattern_copied
    loop .copy_pattern
    
.pattern_copied:
    ; Perform pattern matching (simplified implementation)
    call simple_pattern_match
    
    ; Return match count
    mov eax, [match_count]
    
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; String replace function
; Parameters: string, old_substring, new_substring
; Returns: pointer to result string
पाठ्य_बदलें:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    mov esi, [ebp + 8]              ; original string
    mov ebx, [ebp + 12]             ; old substring
    mov edx, [ebp + 16]             ; new substring
    
    mov edi, result_buffer
    
.replace_loop:
    ; Check if we found the substring to replace
    push esi
    push ebx
    call string_find_substring
    add esp, 8
    
    cmp eax, -1
    je .copy_rest
    
    ; Copy characters before the match
    mov ecx, eax
    rep movsb
    
    ; Skip the old substring
    call get_string_length
    push ebx
    call get_string_length
    add esp, 4
    add esi, eax
    
    ; Copy the new substring
    push edx
    call copy_string_to_buffer
    add esp, 4
    
    jmp .replace_loop
    
.copy_rest:
    ; Copy remaining characters
.copy_remaining:
    lodsb
    stosb
    test al, al
    jnz .copy_remaining
    
    ; Return result buffer
    mov eax, result_buffer
    
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; String split function
; Parameters: string, delimiter
; Returns: pointer to array of substrings
पाठ्य_विभाजन:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    mov esi, [ebp + 8]              ; string
    mov bl, [ebp + 12]              ; delimiter (single character for simplicity)
    
    mov edi, split_results
    mov dword [match_count], 0      ; Use as split count
    
.split_loop:
    ; Find next delimiter
    mov edx, esi
    
.find_delimiter:
    lodsb
    test al, al
    jz .last_part
    cmp al, bl
    je .found_delimiter
    jmp .find_delimiter
    
.found_delimiter:
    ; Calculate length of current part
    mov eax, esi
    sub eax, edx
    dec eax                         ; Exclude delimiter
    
    ; Store part information
    mov [edi], edx                  ; Start address
    mov [edi + 4], eax              ; Length
    add edi, 8
    
    inc dword [match_count]
    jmp .split_loop
    
.last_part:
    ; Handle last part
    mov eax, esi
    sub eax, edx
    dec eax
    
    cmp eax, 0
    jle .split_done
    
    mov [edi], edx
    mov [edi + 4], eax
    inc dword [match_count]
    
.split_done:
    mov eax, split_results
    
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Convert to uppercase
; Parameters: string
; Returns: pointer to uppercase string
बड़े_अक्षर_में:
    push ebp
    mov ebp, esp
    push esi
    push edi
    
    mov esi, [ebp + 8]              ; input string
    mov edi, result_buffer
    
.convert_loop:
    lodsb
    test al, al
    jz .convert_done
    
    ; Check if lowercase letter
    cmp al, 'a'
    jb .not_lowercase
    cmp al, 'z'
    ja .not_lowercase
    
    ; Convert to uppercase
    sub al, 32
    
.not_lowercase:
    stosb
    jmp .convert_loop
    
.convert_done:
    mov al, 0
    stosb
    
    mov eax, result_buffer
    
    pop edi
    pop esi
    mov esp, ebp
    pop ebp
    ret

; Convert to lowercase
; Parameters: string
; Returns: pointer to lowercase string
छोटे_अक्षर_में:
    push ebp
    mov ebp, esp
    push esi
    push edi
    
    mov esi, [ebp + 8]              ; input string
    mov edi, result_buffer
    
.convert_loop:
    lodsb
    test al, al
    jz .convert_done
    
    ; Check if uppercase letter
    cmp al, 'A'
    jb .not_uppercase
    cmp al, 'Z'
    ja .not_uppercase
    
    ; Convert to lowercase
    add al, 32
    
.not_uppercase:
    stosb
    jmp .convert_loop
    
.convert_done:
    mov al, 0
    stosb
    
    mov eax, result_buffer
    
    pop edi
    pop esi
    mov esp, ebp
    pop ebp
    ret

; Check if string contains substring
; Parameters: string, substring
; Returns: 1 if contains, 0 if not
पाठ्य_शामिल:
    push ebp
    mov ebp, esp
    
    push dword [ebp + 12]           ; substring
    push dword [ebp + 8]            ; string
    call string_find_substring
    add esp, 8
    
    cmp eax, -1
    je .not_found
    
    mov eax, 1
    jmp .done
    
.not_found:
    xor eax, eax
    
.done:
    mov esp, ebp
    pop ebp
    ret

; Helper function: find substring in string
; Parameters: string, substring
; Returns: position if found, -1 if not found
string_find_substring:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    mov esi, [ebp + 8]              ; string
    mov edi, [ebp + 12]             ; substring
    
    ; Get substring length
    push edi
    call get_string_length
    add esp, 4
    mov ebx, eax                    ; substring length
    
    cmp ebx, 0
    je .not_found
    
    mov edx, 0                      ; position counter
    
.search_loop:
    ; Check if we have enough characters left
    push esi
    call get_string_length
    add esp, 4
    
    cmp eax, ebx
    jb .not_found
    
    ; Compare substring
    push ebx
    push edi
    push esi
    call compare_strings
    add esp, 12
    
    cmp eax, 1
    je .found
    
    ; Move to next character
    inc esi
    inc edx
    jmp .search_loop
    
.found:
    mov eax, edx
    jmp .done
    
.not_found:
    mov eax, -1
    
.done:
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Helper functions (simplified implementations)
simple_pattern_match:
    ; Simplified pattern matching - would need full regex engine
    mov dword [match_count], 1
    ret

get_string_length:
    push ebp
    mov ebp, esp
    push esi
    
    mov esi, [ebp + 8]
    xor eax, eax
    
.length_loop:
    cmp byte [esi + eax], 0
    je .length_done
    inc eax
    jmp .length_loop
    
.length_done:
    pop esi
    mov esp, ebp
    pop ebp
    ret

compare_strings:
    push ebp
    mov ebp, esp
    push esi
    push edi
    
    mov esi, [ebp + 8]              ; string1
    mov edi, [ebp + 12]             ; string2
    mov ecx, [ebp + 16]             ; length
    
    repe cmpsb
    sete al
    movzx eax, al
    
    pop edi
    pop esi
    mov esp, ebp
    pop ebp
    ret

copy_string_to_buffer:
    push ebp
    mov ebp, esp
    push esi
    
    mov esi, [ebp + 8]
    
.copy_loop:
    lodsb
    stosb
    test al, al
    jnz .copy_loop
    
    pop esi
    mov esp, ebp
    pop ebp
    ret
