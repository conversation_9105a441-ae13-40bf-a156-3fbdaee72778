# द्विभाषी गणक उदाहरण / Bilingual Calculator Example

# संस्कृत में कार्यान्वयन / Implementation in Sanskrit
कार्य संस्कृत_गणक() {
    लेखय("*** संस्कृत गणक ***")
    लेखय("कृपया पहली संख्या दर्ज करें:")
    परिवर्तनीय संख्या१ = पठ()
    
    लेखय("कृपया दूसरी संख्या दर्ज करें:")
    परिवर्तनीय संख्या२ = पठ()
    
    परिवर्तनीय योग = संख्या१ + संख्या२
    परिवर्तनीय अंतर = संख्या१ - संख्या२
    परिवर्तनीय गुणन = संख्या१ * संख्या२
    परिवर्तनीय भाग = संख्या१ / संख्या२
    
    लेखय("परिणाम:")
    लेखय("योग: " + योग)
    लेखय("अंतर: " + अंतर)
    लेखय("गुणन: " + गुणन)
    लेखय("भाग: " + भाग)
}

# हिंदी में कार्यान्वयन / Implementation in Hindi
कार्य हिंदी_गणक() {
    लिखो("*** हिंदी गणक ***")
    लिखो("पहली संख्या डालें:")
    चर संख्या१ = पढ़ो()
    
    लिखो("दूसरी संख्या डालें:")
    चर संख्या२ = पढ़ो()
    
    चर जोड़ = संख्या१ + संख्या२
    चर घटाव = संख्या१ - संख्या२
    चर गुणा = संख्या१ * संख्या२
    चर भाग = संख्या१ / संख्या२
    
    लिखो("परिणाम:")
    लिखो("जोड़: " + जोड़)
    लिखो("घटाव: " + घटाव)
    लिखो("गुणा: " + गुणा)
    लिखो("भाग: " + भाग)
}

# मुख्य कार्यक्रम / Main Program
लेखय("भाषा चुनें / Choose language:")
लेखय("1. संस्कृत / Sanskrit")
लेखय("2. हिंदी / Hindi")

परिवर्तनीय विकल्प = पठ()
यदि (विकल्प == १) {
    संस्कृत_गणक()
} अन्यथा {
    हिंदी_गणक()
}