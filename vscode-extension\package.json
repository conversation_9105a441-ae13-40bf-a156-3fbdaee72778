{"name": "sanskrit-programming-language", "displayName": "Sanskrit Programming Language", "description": "Language support for Sanskrit Programming Language (SPL)", "version": "1.0.0", "publisher": "sanskrit-lang", "engines": {"vscode": "^1.74.0"}, "categories": ["Programming Languages", "Syntax Highlighting", "Debuggers", "Formatters"], "keywords": ["sanskrit", "<PERSON><PERSON><PERSON><PERSON>", "programming", "language", "spl"], "main": "./out/extension.js", "contributes": {"languages": [{"id": "sanskrit", "aliases": ["Sanskrit", "SPL", "sanskrit"], "extensions": [".snkt"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "sanskrit", "scopeName": "source.sanskrit", "path": "./syntaxes/sanskrit.tmLanguage.json"}], "themes": [{"label": "Sanskrit Dark", "uiTheme": "vs-dark", "path": "./themes/sanskrit-dark.json"}, {"label": "Sanskrit Light", "uiTheme": "vs", "path": "./themes/sanskrit-light.json"}], "commands": [{"command": "sanskrit.runFile", "title": "Run Sanskrit File", "category": "Sanskrit"}, {"command": "sanskrit.debugFile", "title": "Debug Sanskrit File", "category": "Sanskrit"}, {"command": "sanskrit.formatDocument", "title": "Format Sanskrit Document", "category": "Sanskrit"}, {"command": "sanskrit.showDocumentation", "title": "Show Documentation", "category": "Sanskrit"}, {"command": "sanskrit.transpileToJS", "title": "Transpile to JavaScript", "category": "Sanskrit"}, {"command": "sanskrit.transpileToPython", "title": "Transpile to Python", "category": "Sanskrit"}], "keybindings": [{"command": "sanskrit.runFile", "key": "ctrl+f5", "when": "editorTextFocus && resourceExtname == .snkt"}, {"command": "sanskrit.debugFile", "key": "f5", "when": "editorTextFocus && resourceExtname == .snkt"}], "menus": {"editor/context": [{"when": "resourceExtname == .snkt", "command": "sanskrit.runFile", "group": "navigation"}, {"when": "resourceExtname == .snkt", "command": "sanskrit.formatDocument", "group": "1_modification"}], "explorer/context": [{"when": "resourceExtname == .snkt", "command": "sanskrit.runFile", "group": "navigation"}]}, "configuration": {"type": "object", "title": "Sanskrit Language Configuration", "properties": {"sanskrit.interpreterPath": {"type": "string", "default": "sanskrit", "description": "Path to the Sanskrit interpreter"}, "sanskrit.enableLinting": {"type": "boolean", "default": true, "description": "Enable linting for Sanskrit files"}, "sanskrit.enableAutoComplete": {"type": "boolean", "default": true, "description": "Enable auto-completion"}, "sanskrit.enableTypeChecking": {"type": "boolean", "default": true, "description": "Enable type checking"}, "sanskrit.formatOnSave": {"type": "boolean", "default": false, "description": "Format document on save"}, "sanskrit.showDevanagariInput": {"type": "boolean", "default": true, "description": "Show Devanagari input helper"}, "sanskrit.transliterationMode": {"type": "string", "enum": ["none", "iast", "harvard-kyoto", "itrans"], "default": "none", "description": "Transliteration input mode"}}}, "snippets": [{"language": "sanskrit", "path": "./snippets/sanskrit.json"}], "debuggers": [{"type": "sanskrit", "label": "Sanskrit Debug", "program": "./out/debugAdapter.js", "runtime": "node", "configurationAttributes": {"launch": {"required": ["program"], "properties": {"program": {"type": "string", "description": "Absolute path to a Sanskrit file.", "default": "${workspaceFolder}/${command:AskForProgramName}"}, "stopOnEntry": {"type": "boolean", "description": "Automatically stop after launch.", "default": true}, "trace": {"type": "boolean", "description": "Enable logging of the Debug Adapter Protocol.", "default": true}}}}, "initialConfigurations": [{"type": "sanskrit", "request": "launch", "name": "Launch Sanskrit Program", "program": "${workspaceFolder}/${command:AskForProgramName}", "stopOnEntry": true}], "configurationSnippets": [{"label": "Sanskrit: Launch", "description": "A new configuration for launching a Sanskrit program", "body": {"type": "sanskrit", "request": "launch", "name": "Launch Sanskrit Program", "program": "^\"\\${workspaceFolder}/\\${command:AskForProgramName}\"", "stopOnEntry": true}}]}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4"}, "dependencies": {"vscode-languageclient": "^8.0.2", "vscode-languageserver": "^8.0.2", "vscode-languageserver-textdocument": "^1.0.7"}, "repository": {"type": "git", "url": "https://github.com/sanskrit-lang/vscode-extension"}, "bugs": {"url": "https://github.com/sanskrit-lang/vscode-extension/issues"}, "homepage": "https://github.com/sanskrit-lang/vscode-extension#readme", "license": "MIT"}