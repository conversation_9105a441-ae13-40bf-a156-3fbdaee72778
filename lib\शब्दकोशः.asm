section .data
    ; Dictionary-related keywords and messages
    kw_dict db 'शब्दकोशः', 0     ; Dictionary keyword
    kw_set db 'स्थापय', 0        ; Set key-value
    kw_get db 'प्राप्त', 0       ; Get value
    kw_del db 'विलोप', 0         ; Delete key-value
    kw_has db 'अस्ति', 0         ; Check if key exists
    kw_keys db 'कुञ्जिकाः', 0    ; Get all keys

    dict_error_full db 'त्रुटि: Dictionary is full', 0xA
    dict_error_full_len equ $ - dict_error_full
    dict_error_notfound db 'त्रुटि: Key not found', 0xA
    dict_error_notfound_len equ $ - dict_error_notfound

    ; Dictionary configuration
    max_dicts equ 16            ; Maximum number of dictionaries
    dict_size equ 100          ; Size of each dictionary
    max_key_size equ 32        ; Maximum key length
    max_value_size equ 64      ; Maximum value length

section .bss
    ; Dictionary storage
    dict_entries resb max_dicts * dict_size * (max_key_size + max_value_size)
    dict_lengths resb max_dicts * 4   ; Length of each dictionary
    dict_count dd 0                  ; Number of dictionaries

section .text
    global dict_init
    global dict_set
    global dict_get
    global dict_del
    global dict_has
    global dict_keys

; Initialize a new dictionary
dict_init:
    push ebp
    mov ebp, esp
    
    ; Check if we can create more dictionaries
    mov eax, [dict_count]
    cmp eax, max_dicts
    jge dict_init_error
    
    ; Initialize dictionary length to 0
    mov dword [dict_lengths + eax * 4], 0
    
    ; Increment dictionary count and return dictionary ID
    inc dword [dict_count]
    dec eax
    
    mov esp, ebp
    pop ebp
    ret

; Set key-value pair in dictionary
; Parameters: dict_id (ebp+8), key (ebp+12), value (ebp+16)
dict_set:
    push ebp
    mov ebp, esp
    push esi
    push edi
    
    ; Get dictionary length
    mov eax, [ebp + 8]        ; dict_id
    mov ecx, [dict_lengths + eax * 4]
    
    ; Check if dictionary is full
    cmp ecx, dict_size
    jge dict_full_error
    
    ; Calculate entry position
    imul eax, dict_size * (max_key_size + max_value_size)
    lea edi, [dict_entries + eax + ecx * (max_key_size + max_value_size)]
    
    ; Copy key
    mov esi, [ebp + 12]       ; key
    mov ecx, max_key_size
    rep movsb
    
    ; Copy value
    mov esi, [ebp + 16]       ; value
    mov ecx, max_value_size
    rep movsb
    
    ; Increment length
    mov eax, [ebp + 8]        ; dict_id
    inc dword [dict_lengths + eax * 4]
    
    pop edi
    pop esi
    mov esp, ebp
    pop ebp
    ret 12

; Get value by key from dictionary
; Parameters: dict_id (ebp+8), key (ebp+12)
; Returns: eax = pointer to value (or null if not found)
dict_get:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    ; Get dictionary length
    mov eax, [ebp + 8]        ; dict_id
    mov ecx, [dict_lengths + eax * 4]
    test ecx, ecx
    jz key_not_found
    
    ; Calculate base address
    imul eax, dict_size * (max_key_size + max_value_size)
    lea edi, [dict_entries + eax]
    
    ; Search for key
    mov ebx, ecx              ; Save length
    mov esi, [ebp + 12]       ; key

find_key:
    push ecx
    push esi
    mov ecx, max_key_size
    repe cmpsb
    pop esi
    pop ecx
    je found_key
    
    ; Move to next entry
    add edi, max_value_size
    loop find_key
    
    ; Key not found
    xor eax, eax
    jmp get_done

found_key:
    mov eax, edi             ; Return pointer to value

get_done:
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret 8

; Error handlers
dict_init_error:
dict_full_error:
key_not_found:
    xor eax, eax
    ret