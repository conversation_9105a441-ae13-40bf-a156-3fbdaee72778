const {
  createConnection,
  TextDocuments,
  ProposedFeatures,
  InitializeParams,
  TextDocumentSyncKind,
  CompletionItem,
  CompletionItemKind
} = require('vscode-languageserver/node');

const { TextDocument } = require('vscode-languageserver-textdocument');

// Create a connection for the server
const connection = createConnection(ProposedFeatures.all);

// Create a text document manager
const documents = new TextDocuments(TextDocument);

let hasConfigurationCapability = false;
let hasWorkspaceFolderCapability = false;

connection.onInitialize((params) => {
  const capabilities = params.capabilities;

  hasConfigurationCapability = !!(capabilities.workspace && !!capabilities.workspace.configuration);
  hasWorkspaceFolderCapability = !!(capabilities.workspace && !!capabilities.workspace.workspaceFolders);

  return {
    capabilities: {
      textDocumentSync: TextDocumentSyncKind.Incremental,
      completionProvider: {
        resolveProvider: true,
        triggerCharacters: ['.', '।']
      },
      hoverProvider: true,
      definitionProvider: true,
      diagnosticProvider: {
        interFileDependencies: false,
        workspaceDiagnostics: false
      }
    }
  };
});

// Sanskrit and Hindi keywords
const sanskritKeywords = [
  { label: 'यदि', kind: CompletionItemKind.Keyword, detail: 'Conditional statement (if)' },
  { label: 'अन्यथा', kind: CompletionItemKind.Keyword, detail: 'Else statement' },
  { label: 'पुनरावर्तनम्', kind: CompletionItemKind.Keyword, detail: 'Loop statement' },
  { label: 'कार्य', kind: CompletionItemKind.Keyword, detail: 'Function declaration' },
  { label: 'लेखय', kind: CompletionItemKind.Keyword, detail: 'Print statement' },
  { label: 'परिवर्तनीय', kind: CompletionItemKind.Keyword, detail: 'Variable declaration' }
];

const hindiKeywords = [
  { label: 'अगर', kind: CompletionItemKind.Keyword, detail: 'Conditional statement (if)' },
  { label: 'वरना', kind: CompletionItemKind.Keyword, detail: 'Else statement' },
  { label: 'दोहराओ', kind: CompletionItemKind.Keyword, detail: 'Loop statement' },
  { label: 'कार्य', kind: CompletionItemKind.Keyword, detail: 'Function declaration' },
  { label: 'लिखो', kind: CompletionItemKind.Keyword, detail: 'Print statement' },
  { label: 'चर', kind: CompletionItemKind.Keyword, detail: 'Variable declaration' }
];

// Handle completion requests
connection.onCompletion(async (textDocumentPosition) => {
  return [...sanskritKeywords, ...hindiKeywords];
});

// Handle completion resolve
connection.onCompletionResolve((item) => {
  return item;
});

// Handle hover requests
connection.onHover(({ textDocument, position }) => {
  // Implement hover functionality
  return {
    contents: {
      kind: 'markdown',
      value: 'Documentation for this symbol'
    }
  };
});

// Handle go to definition requests
connection.onDefinition(({ textDocument, position }) => {
  // Implement definition lookup
  return null;
});

// Listen for text document changes
documents.onDidChangeContent(change => {
  validateTextDocument(change.document);
});

async function validateTextDocument(textDocument) {
  // Implement document validation and diagnostics
  const diagnostics = [];
  connection.sendDiagnostics({ uri: textDocument.uri, diagnostics });
}

// Make the text document manager listen on the connection
documents.listen(connection);

// Listen on the connection
connection.listen();