# Text Processing Example in Sanskrit
# Demonstrates string manipulation and data processing features

# Define input text
पाठ्यम् = "संस्कृतम् जगतः प्राचीनतमा भाषा अस्ति"

# Function to count words
शब्दगणना = (पाठः) -> {
    शब्दाः = पाठः.विभाजनम्(" ")
    फलम् = शब्दाः.संख्या
    प्रतिफलम् फलम्
}

# Function to reverse text
विपरीतपाठः = (पाठः) -> {
    अक्षराणि = पाठः.सूची
    विपरीतम् = ""
    क्रमः = अक्षराणि.संख्या - १
    यावत् (क्रमः >= ०) {
        विपरीतम् += अक्षराणि[क्रमः]
        क्रमः -= १
    }
    प्रतिफलम् विपरीतम्
}

# Process text
मुद्रणम्("मूलपाठः: " + पाठ्यम्)
मुद्रणम्("शब्दसंख्या: " + शब्दगणना(पाठ्यम्))
मुद्रणम्("विपरीतपाठः: " + विपरीतपाठः(पाठ्यम्))