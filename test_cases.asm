; Test cases for Sanskrit language interpreter

section .data
    ; Test prompts
    test_arith db 'Testing arithmetic operations...', 0xA
    test_cond db 'Testing conditional branching...', 0xA
    test_loop db 'Testing loop structures...', 0xA
    test_func db 'Testing functions...', 0xA
    test_error db 'Testing error cases...', 0xA

    ; Test inputs
    input_add db 'परिवर्तनीय क = १० + २०', 0
    input_sub db 'परिवर्तनीय ख = ३० - १५', 0
    input_mul db 'परिवर्तनीय ग = ५ * ६', 0

    ; Conditional test
    input_if db 'यदि क > २० अन्यथा', 0

    ; Loop test
    input_loop db 'पुनरावर्तनम् ५ समाप्त', 0

    ; Function test
    input_func db 'कार्य योग(क, ख) परं क + ख समाप्त', 0

    ; Error test cases
    input_overflow db 'परिवर्तनीय घ = ९९९९९९९९९', 0
    input_uninit db 'लेखय(अ)', 0

section .text
    global _start

_start:
    ; Run arithmetic tests
    mov eax, 4
    mov ebx, 1
    mov ecx, test_arith
    int 0x80

    ; Test addition
    mov esi, input_add
    call parse_command

    ; Test subtraction
    mov esi, input_sub
    call parse_command

    ; Test multiplication
    mov esi, input_mul
    call parse_command

    ; Run conditional tests
    mov eax, 4
    mov ebx, 1
    mov ecx, test_cond
    int 0x80

    mov esi, input_if
    call parse_command

    ; Run loop tests
    mov eax, 4
    mov ebx, 1
    mov ecx, test_loop
    int 0x80

    mov esi, input_loop
    call parse_command

    ; Run function tests
    mov eax, 4
    mov ebx, 1
    mov ecx, test_func
    int 0x80

    mov esi, input_func
    call parse_command

    ; Run error tests
    mov eax, 4
    mov ebx, 1
    mov ecx, test_error
    int 0x80

    mov esi, input_overflow
    call parse_command

    mov esi, input_uninit
    call parse_command

    ; Exit program
    mov eax, 1
    xor ebx, ebx
    int 0x80