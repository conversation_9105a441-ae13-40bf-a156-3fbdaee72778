; Networking Library for Sanskrit Programming Language
; जाल संचार (Network Communication)
; HTTP, WebSocket, TCP/UDP support

section .data
    ; Networking keywords in Sanskrit
    kw_http db 'एचटीटीपी', 0           ; HTTP
    kw_websocket db 'वेबसॉकेट', 0      ; WebSocket
    kw_tcp db 'टीसीपी', 0              ; TCP
    kw_udp db 'यूडीपी', 0              ; UDP
    kw_server db 'सर्वर', 0            ; Server
    kw_client db 'ग्राहक', 0           ; Client
    kw_request db 'अनुरोध', 0          ; Request
    kw_response db 'प्रतिक्रिया', 0     ; Response
    
    ; HTTP methods
    http_get db 'GET', 0
    http_post db 'POST', 0
    http_put db 'PUT', 0
    http_delete db 'DELETE', 0
    http_patch db 'PATCH', 0
    http_head db 'HEAD', 0
    http_options db 'OPTIONS', 0
    
    ; HTTP status codes and messages
    status_200 db 'HTTP/1.1 200 OK', 0xD, 0xA
    status_404 db 'HTTP/1.1 404 Not Found', 0xD, 0xA
    status_500 db 'HTTP/1.1 500 Internal Server Error', 0xD, 0xA
    
    ; Common HTTP headers
    header_content_type db 'Content-Type: ', 0
    header_content_length db 'Content-Length: ', 0
    header_connection db 'Connection: ', 0
    header_server db 'Server: Sanskrit-HTTP/1.0', 0xD, 0xA
    
    ; Content types
    content_html db 'text/html; charset=utf-8', 0xD, 0xA
    content_json db 'application/json; charset=utf-8', 0xD, 0xA
    content_text db 'text/plain; charset=utf-8', 0xD, 0xA
    
    ; Error messages
    error_socket_create db 'त्रुटि: सॉकेट बनाने में असफल', 0xA
    error_bind_failed db 'त्रुटि: बाइंड असफल', 0xA
    error_listen_failed db 'त्रुटि: लिसन असफल', 0xA
    error_connect_failed db 'त्रुटि: कनेक्शन असफल', 0xA
    error_send_failed db 'त्रुटि: डेटा भेजने में असफल', 0xA
    error_recv_failed db 'त्रुटि: डेटा प्राप्त करने में असफल', 0xA
    
    ; Socket constants
    AF_INET equ 2
    SOCK_STREAM equ 1
    SOCK_DGRAM equ 2
    IPPROTO_TCP equ 6
    IPPROTO_UDP equ 17
    
    ; Default ports
    HTTP_PORT equ 80
    HTTPS_PORT equ 443
    DEFAULT_SERVER_PORT equ 8080

section .bss
    ; Socket descriptors
    server_socket resd 1            ; Server socket descriptor
    client_socket resd 1            ; Client socket descriptor
    
    ; Network buffers
    request_buffer resb 4096        ; HTTP request buffer
    response_buffer resb 4096       ; HTTP response buffer
    send_buffer resb 2048           ; Send buffer
    recv_buffer resb 2048           ; Receive buffer
    
    ; Address structures
    server_addr resb 16             ; Server address structure
    client_addr resb 16             ; Client address structure
    addr_len resd 1                 ; Address length
    
    ; HTTP parsing state
    http_method resb 16             ; HTTP method
    http_path resb 256              ; Request path
    http_version resb 16            ; HTTP version
    http_headers resb 1024          ; HTTP headers
    http_body resb 2048             ; HTTP body
    
    ; Connection state
    is_connected resd 1             ; Connection status
    current_port resd 1             ; Current port number

section .text
    global जाल_प्रारम्भ               ; network_init
    global सर्वर_प्रारम्भ              ; start_server
    global ग्राहक_कनेक्ट               ; client_connect
    global एचटीटीपी_अनुरोध             ; http_request
    global एचटीटीपी_प्रतिक्रिया         ; http_response
    global डेटा_भेजें                  ; send_data
    global डेटा_प्राप्त                ; receive_data
    global कनेक्शन_बंद                ; close_connection
    global जेसन_पार्स                 ; parse_json
    global जेसन_बनाएं                 ; create_json
    global वेबसॉकेट_अपग्रेड            ; websocket_upgrade
    global वेबसॉकेट_संदेश              ; websocket_message

; Initialize networking module
जाल_प्रारम्भ:
    push ebp
    mov ebp, esp
    
    ; Initialize socket descriptors
    mov dword [server_socket], -1
    mov dword [client_socket], -1
    mov dword [is_connected], 0
    mov dword [current_port], DEFAULT_SERVER_PORT
    
    ; Clear buffers
    mov edi, request_buffer
    mov ecx, 4096
    xor eax, eax
    rep stosb
    
    mov esp, ebp
    pop ebp
    ret

; Start HTTP server
; Parameters: port
सर्वर_प्रारम्भ:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    
    ; Get port parameter
    mov eax, [ebp + 8]
    mov [current_port], eax
    
    ; Create socket
    mov eax, 102                        ; sys_socketcall
    mov ebx, 1                          ; SYS_SOCKET
    push IPPROTO_TCP
    push SOCK_STREAM
    push AF_INET
    mov ecx, esp
    int 0x80
    add esp, 12
    
    cmp eax, 0
    jl .socket_error
    mov [server_socket], eax
    
    ; Setup server address
    mov edi, server_addr
    mov word [edi], AF_INET             ; sin_family
    mov eax, [current_port]
    xchg al, ah                         ; Convert to network byte order
    mov word [edi + 2], ax              ; sin_port
    mov dword [edi + 4], 0              ; sin_addr (INADDR_ANY)
    
    ; Bind socket
    mov eax, 102                        ; sys_socketcall
    mov ebx, 2                          ; SYS_BIND
    push 16                             ; Address length
    push server_addr
    push dword [server_socket]
    mov ecx, esp
    int 0x80
    add esp, 12
    
    cmp eax, 0
    jl .bind_error
    
    ; Listen for connections
    mov eax, 102                        ; sys_socketcall
    mov ebx, 4                          ; SYS_LISTEN
    push 5                              ; Backlog
    push dword [server_socket]
    mov ecx, esp
    int 0x80
    add esp, 8
    
    cmp eax, 0
    jl .listen_error
    
    ; Server started successfully
    mov eax, 1
    jmp .done
    
.socket_error:
    mov eax, 4
    mov ebx, 2                          ; stderr
    mov ecx, error_socket_create
    mov edx, 35
    int 0x80
    xor eax, eax
    jmp .done
    
.bind_error:
    mov eax, 4
    mov ebx, 2
    mov ecx, error_bind_failed
    mov edx, 20
    int 0x80
    xor eax, eax
    jmp .done
    
.listen_error:
    mov eax, 4
    mov ebx, 2
    mov ecx, error_listen_failed
    mov edx, 22
    int 0x80
    xor eax, eax
    
.done:
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Accept client connection
accept_client:
    push ebp
    mov ebp, esp
    
    ; Accept connection
    mov dword [addr_len], 16
    mov eax, 102                        ; sys_socketcall
    mov ebx, 5                          ; SYS_ACCEPT
    push addr_len
    push client_addr
    push dword [server_socket]
    mov ecx, esp
    int 0x80
    add esp, 12
    
    cmp eax, 0
    jl .accept_error
    
    mov [client_socket], eax
    mov dword [is_connected], 1
    jmp .done
    
.accept_error:
    mov dword [client_socket], -1
    mov dword [is_connected], 0
    
.done:
    mov esp, ebp
    pop ebp
    ret

; Send HTTP response
; Parameters: status_code, content_type, body
एचटीटीपी_प्रतिक्रिया:
    push ebp
    mov ebp, esp
    push ebx
    push esi
    push edi
    
    mov eax, [ebp + 8]                  ; status_code
    mov ebx, [ebp + 12]                 ; content_type
    mov ecx, [ebp + 16]                 ; body
    
    ; Build HTTP response
    mov edi, response_buffer
    
    ; Add status line
    cmp eax, 200
    je .status_200
    cmp eax, 404
    je .status_404
    jmp .status_500
    
.status_200:
    mov esi, status_200
    call copy_string
    jmp .add_headers
    
.status_404:
    mov esi, status_404
    call copy_string
    jmp .add_headers
    
.status_500:
    mov esi, status_500
    call copy_string
    
.add_headers:
    ; Add server header
    mov esi, header_server
    call copy_string
    
    ; Add content type header
    mov esi, header_content_type
    call copy_string
    mov esi, ebx                        ; content_type
    call copy_string
    
    ; Add content length header
    mov esi, header_content_length
    call copy_string
    
    ; Calculate content length
    push ecx
    call string_length
    add esp, 4
    call number_to_string
    call copy_string
    
    ; Add CRLF
    mov al, 0xD
    stosb
    mov al, 0xA
    stosb
    
    ; Add another CRLF to separate headers from body
    mov al, 0xD
    stosb
    mov al, 0xA
    stosb
    
    ; Add body
    mov esi, ecx                        ; body
    call copy_string
    
    ; Send response
    push response_buffer
    call send_data
    add esp, 4
    
    pop edi
    pop esi
    pop ebx
    mov esp, ebp
    pop ebp
    ret

; Send data over socket
; Parameters: data
डेटा_भेजें:
    push ebp
    mov ebp, esp
    
    mov esi, [ebp + 8]                  ; data
    
    ; Calculate data length
    push esi
    call string_length
    add esp, 4
    mov edx, eax                        ; length
    
    ; Send data
    mov eax, 102                        ; sys_socketcall
    mov ebx, 9                          ; SYS_SEND
    push 0                              ; flags
    push edx                            ; length
    push esi                            ; data
    push dword [client_socket]
    mov ecx, esp
    int 0x80
    add esp, 16
    
    mov esp, ebp
    pop ebp
    ret

; Receive data from socket
; Returns: pointer to received data
डेटा_प्राप्त:
    push ebp
    mov ebp, esp
    
    ; Receive data
    mov eax, 102                        ; sys_socketcall
    mov ebx, 10                         ; SYS_RECV
    push 0                              ; flags
    push 2048                           ; buffer size
    push recv_buffer
    push dword [client_socket]
    mov ecx, esp
    int 0x80
    add esp, 16
    
    cmp eax, 0
    jle .recv_error
    
    ; Null terminate received data
    mov [recv_buffer + eax], byte 0
    mov eax, recv_buffer
    jmp .done
    
.recv_error:
    xor eax, eax
    
.done:
    mov esp, ebp
    pop ebp
    ret

; Close connection
कनेक्शन_बंद:
    push ebp
    mov ebp, esp
    
    ; Close client socket
    cmp dword [client_socket], -1
    je .close_server
    
    mov eax, 6                          ; sys_close
    mov ebx, [client_socket]
    int 0x80
    mov dword [client_socket], -1
    
.close_server:
    ; Close server socket
    cmp dword [server_socket], -1
    je .done
    
    mov eax, 6                          ; sys_close
    mov ebx, [server_socket]
    int 0x80
    mov dword [server_socket], -1
    
.done:
    mov dword [is_connected], 0
    
    mov esp, ebp
    pop ebp
    ret

; Helper function to copy string
copy_string:
    push ebp
    mov ebp, esp
    
.copy_loop:
    lodsb
    test al, al
    jz .copy_done
    stosb
    jmp .copy_loop
    
.copy_done:
    mov esp, ebp
    pop ebp
    ret

; Helper function to get string length
string_length:
    push ebp
    mov ebp, esp
    push esi
    
    mov esi, [ebp + 8]
    xor eax, eax
    
.length_loop:
    cmp byte [esi + eax], 0
    je .length_done
    inc eax
    jmp .length_loop
    
.length_done:
    pop esi
    mov esp, ebp
    pop ebp
    ret

; Helper function to convert number to string
number_to_string:
    push ebp
    mov ebp, esp
    
    ; Simplified number to string conversion
    ; (Would need full implementation)
    mov al, '0'
    add al, [ebp + 8]
    stosb
    
    mov esp, ebp
    pop ebp
    ret
