; Test file for Sanskrit Language Interpreter
section .data
    test_header db 'Running Sanskrit Language Tests', 0xA
    test_header_len equ $ - test_header
    test_pass db 'Test Passed: ', 0
    test_pass_len equ $ - test_pass
    test_fail db 'Test Failed: ', 0
    test_fail_len equ $ - test_fail
    newline db 0xA

section .text
    global _start

_start:
    ; Print test header
    mov eax, 4
    mov ebx, 1
    mov ecx, test_header
    mov edx, test_header_len
    int 0x80

    ; Test 1: Variable Declaration
    ; Expected: परिवर्तनीय क = १०
    mov esi, test1_input
    call parse_command
    ; Verify variable exists and has correct value
    mov edi, var_table
    mov eax, [edi + 4]
    cmp eax, 10
    jne test1_fail

    ; Print test pass message
    mov eax, 4
    mov ebx, 1
    mov ecx, test_pass
    mov edx, test_pass_len
    int 0x80

    ; Exit program
    mov eax, 1
    xor ebx, ebx
    int 0x80

test1_fail:
    mov eax, 4
    mov ebx, 1
    mov ecx, test_fail
    mov edx, test_fail_len
    int 0x80
    jmp exit_program

section .data
    test1_input db 'परिवर्तनीय क = १०', 0