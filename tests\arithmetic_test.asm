; Test file for arithmetic operations in Sanskrit
section .data
    test_header db 'Running Arithmetic Tests', 0xA
    test_header_len equ $ - test_header
    test_pass db 'Test Passed: ', 0
    test_pass_len equ $ - test_pass
    test_fail db 'Test Failed: ', 0
    test_fail_len equ $ - test_fail
    newline db 0xA

    ; Test inputs
    test_add db 'परिवर्तनीय क = ५ + ३', 0
    test_sub db 'परिवर्तनीय ख = १० - ४', 0
    test_mul db 'परिवर्तनीय ग = ६ * २', 0

    ; Expected results
    expected_add equ 8
    expected_sub equ 6
    expected_mul equ 12

section .text
    global _start

_start:
    ; Print test header
    mov eax, 4
    mov ebx, 1
    mov ecx, test_header
    mov edx, test_header_len
    int 0x80

    ; Test 1: Addition
    mov esi, test_add
    call parse_command
    mov edi, var_table
    mov eax, [edi + 4]
    cmp eax, expected_add
    jne test1_fail

    ; Test 2: Subtraction
    mov esi, test_sub
    call parse_command
    mov edi, var_table
    add edi, 8
    mov eax, [edi + 4]
    cmp eax, expected_sub
    jne test2_fail

    ; Test 3: Multiplication
    mov esi, test_mul
    call parse_command
    mov edi, var_table
    add edi, 16
    mov eax, [edi + 4]
    cmp eax, expected_mul
    jne test3_fail

    ; All tests passed
    mov eax, 4
    mov ebx, 1
    mov ecx, test_pass
    mov edx, test_pass_len
    int 0x80
    jmp exit_program

test1_fail:
    mov eax, 4
    mov ebx, 1
    mov ecx, test_fail
    mov edx, test_fail_len
    int 0x80
    mov ecx, test_add
    call print_string
    jmp exit_program

test2_fail:
    mov eax, 4
    mov ebx, 1
    mov ecx, test_fail
    mov edx, test_fail_len
    int 0x80
    mov ecx, test_sub
    call print_string
    jmp exit_program

test3_fail:
    mov eax, 4
    mov ebx, 1
    mov ecx, test_fail
    mov edx, test_fail_len
    int 0x80
    mov ecx, test_mul
    call print_string
    jmp exit_program

exit_program:
    mov eax, 1
    xor ebx, ebx
    int 0x80

; Helper function to print null-terminated string
print_string:
    push ecx
    mov edx, 0
count_loop:
    cmp byte [ecx + edx], 0
    je print_now
    inc edx
    jmp count_loop
print_now:
    mov eax, 4
    mov ebx, 1
    pop ecx
    int 0x80
    ret