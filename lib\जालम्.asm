section .data
    ; HTTP method strings
    method_get db 'GET', 0
    method_post db 'POST', 0
    method_put db 'PUT', 0
    method_delete db 'DELETE', 0

    ; HTTP headers
    header_content_type db 'Content-Type: ', 0
    header_content_length db 'Content-Length: ', 0
    header_host db 'Host: ', 0
    header_end db '\r\n', 0

    ; Error messages
    error_connect db 'Error: Could not connect to server', 0xA
    error_connect_len equ $ - error_connect
    error_send db 'Error: Could not send request', 0xA
    error_send_len equ $ - error_send
    error_receive db 'Error: Could not receive response', 0xA
    error_receive_len equ $ - error_receive

section .text
    global प्रेषण     ; Send HTTP request
    global प्राप्ति    ; Receive HTTP response
    global संयोजन    ; Connect to server
    global विच्छेद    ; Disconnect from server

; Connect to server (संयोजन)
; Parameters:
;   esi = server address
;   ebx = port
; Returns:
;   eax = socket descriptor (or -1 on error)
संयोजन:
    push ebx
    push ecx
    push edx

    ; Create socket
    mov eax, 359        ; sys_socket
    mov ebx, 2          ; AF_INET
    mov ecx, 1          ; SOCK_STREAM
    mov edx, 0          ; protocol
    int 0x80

    test eax, eax
    js connect_error

    ; TODO: Implement connection logic
    ; This would involve setting up sockaddr_in structure
    ; and calling sys_connect

    pop edx
    pop ecx
    pop ebx
    ret

connect_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_connect
    mov edx, error_connect_len
    int 0x80
    mov eax, -1
    pop edx
    pop ecx
    pop ebx
    ret

; Send HTTP request (प्रेषण)
; Parameters:
;   eax = socket descriptor
;   esi = request buffer
;   edx = request length
; Returns:
;   eax = bytes sent (or -1 on error)
प्रेषण:
    push ebx
    push ecx

    mov ebx, eax        ; socket descriptor
    mov eax, 363        ; sys_send
    mov ecx, esi        ; request buffer
    int 0x80

    test eax, eax
    js send_error

    pop ecx
    pop ebx
    ret

send_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_send
    mov edx, error_send_len
    int 0x80
    mov eax, -1
    pop ecx
    pop ebx
    ret

; Receive HTTP response (प्राप्ति)
; Parameters:
;   eax = socket descriptor
;   edi = response buffer
;   edx = buffer size
; Returns:
;   eax = bytes received (or -1 on error)
प्राप्ति:
    push ebx
    push ecx

    mov ebx, eax        ; socket descriptor
    mov eax, 364        ; sys_recv
    mov ecx, edi        ; response buffer
    int 0x80

    test eax, eax
    js receive_error

    pop ecx
    pop ebx
    ret

receive_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_receive
    mov edx, error_receive_len
    int 0x80
    mov eax, -1
    pop ecx
    pop ebx
    ret

; Disconnect from server (विच्छेद)
; Parameters:
;   eax = socket descriptor
; Returns: none
विच्छेद:
    push ebx

    mov ebx, eax
    mov eax, 361        ; sys_close
    int 0x80

    pop ebx
    ret