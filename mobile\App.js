import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  ScrollView,
  Alert,
  Dimensions,
  Modal
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Voice from '@react-native-voice/voice';

const { width, height } = Dimensions.get('window');

const App = () => {
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('नमस्ते! Sanskrit Programming Language Mobile REPL में आपका स्वागत है।\n');
  const [history, setHistory] = useState([]);
  const [isListening, setIsListening] = useState(false);
  const [voiceResult, setVoiceResult] = useState('');
  const [showVoiceModal, setShowVoiceModal] = useState(false);
  const [transliterationMode, setTransliterationMode] = useState('devanagari');
  const scrollViewRef = useRef();

  // Sample Sanskrit code templates
  const sampleCodes = [
    'लेखय("नमस्ते संसार!")',
    'परिवर्तनीय संख्या = १०\nलेखय(संख्या)',
    'यदि ५ > ३ {\n    लेखय("सत्य")\n} अन्यथा {\n    लेखय("मिथ्या")\n}',
    'कार्य योग(क, ख) {\n    प्रत्यावर्तनम् क + ख\n}\nलेखय(योग(५, ३))',
    'पुनरावृत्ति ३ > ० {\n    लेखय("गिनती")\n    ३ = ३ - १\n}',
    'चयन दिन {\n    १ -> लेखय("सोमवार")\n    २ -> लेखय("मंगलवार")\n    _ -> लेखय("अन्य")\n}'
  ];

  // Voice recognition keywords mapping
  const voiceKeywords = {
    'लेखय': ['लेखय', 'लिखो', 'प्रिंट', 'print'],
    'परिवर्तनीय': ['परिवर्तनीय', 'वेरिएबल', 'variable'],
    'कार्य': ['कार्य', 'फंक्शन', 'function'],
    'यदि': ['यदि', 'अगर', 'if'],
    'अन्यथा': ['अन्यथा', 'नहीं तो', 'else'],
    'पुनरावृत्ति': ['पुनरावृत्ति', 'लूप', 'loop', 'while'],
    'चयन': ['चयन', 'स्विच', 'switch'],
    'प्रयत्नः': ['प्रयत्नः', 'ट्राई', 'try'],
    'ग्रहणम्': ['ग्रहणम्', 'कैच', 'catch']
  };

  // Voice recognition setup
  useEffect(() => {
    Voice.onSpeechStart = onSpeechStart;
    Voice.onSpeechEnd = onSpeechEnd;
    Voice.onSpeechResults = onSpeechResults;
    Voice.onSpeechError = onSpeechError;

    loadSavedCode();

    return () => {
      Voice.destroy().then(Voice.removeAllListeners);
    };
  }, []);

  const onSpeechStart = () => {
    setIsListening(true);
  };

  const onSpeechEnd = () => {
    setIsListening(false);
  };

  const onSpeechResults = (event) => {
    const result = event.value[0];
    setVoiceResult(result);

    // Convert voice input to Sanskrit code
    const sanskritCode = convertVoiceToSanskrit(result);
    setCode(prevCode => prevCode + sanskritCode + '\n');
    setShowVoiceModal(false);
  };

  const onSpeechError = (event) => {
    console.log('Speech error:', event.error);
    setIsListening(false);
    setShowVoiceModal(false);
    Alert.alert('Voice Error', 'Could not recognize speech. Please try again.');
  };

  const convertVoiceToSanskrit = (voiceText) => {
    let sanskritCode = voiceText.toLowerCase();

    // Replace voice commands with Sanskrit equivalents
    Object.keys(voiceKeywords).forEach(sanskritWord => {
      voiceKeywords[sanskritWord].forEach(voiceWord => {
        const regex = new RegExp(`\\b${voiceWord}\\b`, 'gi');
        sanskritCode = sanskritCode.replace(regex, sanskritWord);
      });
    });

    // Handle common patterns
    sanskritCode = sanskritCode
      .replace(/print\s+(.+)/gi, 'लेखय("$1")')
      .replace(/variable\s+(\w+)\s*equals?\s*(.+)/gi, 'परिवर्तनीय $1 = $2')
      .replace(/function\s+(\w+)/gi, 'कार्य $1')
      .replace(/if\s+(.+)\s+then/gi, 'यदि $1 {')
      .replace(/else/gi, '} अन्यथा {')
      .replace(/end/gi, '}');

    return sanskritCode;
  };

  // Load saved code from storage
  const loadSavedCode = async () => {
    try {
      const savedCode = await AsyncStorage.getItem('sanskritCode');
      const savedHistory = await AsyncStorage.getItem('sanskritHistory');

      if (savedCode) {
        setCode(savedCode);
      }

      if (savedHistory) {
        setHistory(JSON.parse(savedHistory));
      }
    } catch (error) {
      console.error('Error loading code:', error);
    }
  };

  // Save code to storage
  const saveCode = async () => {
    try {
      await AsyncStorage.setItem('sanskritCode', code);
    } catch (error) {
      console.error('Error saving code:', error);
    }
  };

  // Execute Sanskrit code
  const runCode = () => {
    // Here we'll integrate with the Sanskrit interpreter
    // For now, we'll just display the code as output
    setOutput(`Executing:\n${code}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Sanskrit Programming</Text>
      </View>
      
      <View style={styles.editorContainer}>
        <CodeEditor
          style={styles.editor}
          language="sanskrit"
          syntaxStyle={{
            keyword: { color: '#c792ea' },
            string: { color: '#c3e88d' },
            number: { color: '#f78c6c' },
            comment: { color: '#546e7a' }
          }}
          showLineNumbers
          value={code}
          onChange={setCode}
        />
      </View>

      <View style={styles.controls}>
        <TouchableOpacity style={styles.button} onPress={runCode}>
          <Text style={styles.buttonText}>Run</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button} onPress={saveCode}>
          <Text style={styles.buttonText}>Save</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.outputContainer}>
        <Text style={styles.outputTitle}>Output:</Text>
        <Text style={styles.output}>{output}</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1e1e1e'
  },
  header: {
    padding: 16,
    backgroundColor: '#2d2d2d'
  },
  title: {
    fontSize: 20,
    color: '#fff',
    textAlign: 'center'
  },
  editorContainer: {
    flex: 1,
    margin: 8
  },
  editor: {
    flex: 1,
    backgroundColor: '#2d2d2d'
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 8
  },
  button: {
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 4,
    minWidth: 100,
    alignItems: 'center'
  },
  buttonText: {
    color: '#fff',
    fontSize: 16
  },
  outputContainer: {
    margin: 8,
    padding: 8,
    backgroundColor: '#2d2d2d',
    borderRadius: 4,
    maxHeight: '30%'
  },
  outputTitle: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 8
  },
  output: {
    color: '#fff',
    fontFamily: 'monospace'
  }
});

export default App;