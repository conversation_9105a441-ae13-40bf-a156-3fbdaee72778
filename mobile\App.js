import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import CodeEditor from 'react-native-code-editor';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { WebView } from 'react-native-webview';

const App = () => {
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('');

  // Sample Sanskrit code template
  const sampleCode = `परिवर्तनीय x = १०
लेखय(x)`;

  // Load saved code from storage
  const loadSavedCode = async () => {
    try {
      const savedCode = await AsyncStorage.getItem('sanskritCode');
      if (savedCode) {
        setCode(savedCode);
      } else {
        setCode(sampleCode);
      }
    } catch (error) {
      console.error('Error loading code:', error);
    }
  };

  // Save code to storage
  const saveCode = async () => {
    try {
      await AsyncStorage.setItem('sanskritCode', code);
    } catch (error) {
      console.error('Error saving code:', error);
    }
  };

  // Execute Sanskrit code
  const runCode = () => {
    // Here we'll integrate with the Sanskrit interpreter
    // For now, we'll just display the code as output
    setOutput(`Executing:\n${code}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Sanskrit Programming</Text>
      </View>
      
      <View style={styles.editorContainer}>
        <CodeEditor
          style={styles.editor}
          language="sanskrit"
          syntaxStyle={{
            keyword: { color: '#c792ea' },
            string: { color: '#c3e88d' },
            number: { color: '#f78c6c' },
            comment: { color: '#546e7a' }
          }}
          showLineNumbers
          value={code}
          onChange={setCode}
        />
      </View>

      <View style={styles.controls}>
        <TouchableOpacity style={styles.button} onPress={runCode}>
          <Text style={styles.buttonText}>Run</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button} onPress={saveCode}>
          <Text style={styles.buttonText}>Save</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.outputContainer}>
        <Text style={styles.outputTitle}>Output:</Text>
        <Text style={styles.output}>{output}</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1e1e1e'
  },
  header: {
    padding: 16,
    backgroundColor: '#2d2d2d'
  },
  title: {
    fontSize: 20,
    color: '#fff',
    textAlign: 'center'
  },
  editorContainer: {
    flex: 1,
    margin: 8
  },
  editor: {
    flex: 1,
    backgroundColor: '#2d2d2d'
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 8
  },
  button: {
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 4,
    minWidth: 100,
    alignItems: 'center'
  },
  buttonText: {
    color: '#fff',
    fontSize: 16
  },
  outputContainer: {
    margin: 8,
    padding: 8,
    backgroundColor: '#2d2d2d',
    borderRadius: 4,
    maxHeight: '30%'
  },
  outputTitle: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 8
  },
  output: {
    color: '#fff',
    fontFamily: 'monospace'
  }
});

export default App;