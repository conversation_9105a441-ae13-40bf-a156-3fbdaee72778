section .data
    ; Error messages
    error_process db 'Error: Could not create process', 0xA
    error_process_len equ $ - error_process
    error_file db 'Error: File operation failed', 0xA
    error_file_len equ $ - error_file
    error_memory db 'Error: Memory operation failed', 0xA
    error_memory_len equ $ - error_memory

    ; Buffer sizes
    path_max equ 4096
    name_max equ 255

section .text
    global प्रक्रिया    ; Process management
    global स्मृति      ; Memory management
    global तन्त्रांश    ; System info
    global पठन        ; File read
    global लेखन       ; File write

; Create process (प्रक्रिया)
; Parameters:
;   esi = program path
;   edi = arguments array
; Returns:
;   eax = process ID (or -1 on error)
प्रक्रिया:
    push ebx
    push ecx
    push edx

    mov eax, 2          ; sys_fork
    int 0x80
    
    test eax, eax
    js process_error
    jz child_process

    ; Parent process
    pop edx
    pop ecx
    pop ebx
    ret

child_process:
    ; Execute program
    mov eax, 11         ; sys_execve
    mov ebx, esi        ; program path
    mov ecx, edi        ; arguments
    xor edx, edx        ; environment
    int 0x80

    ; If execve returns, there was an error
    jmp process_error

process_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_process
    mov edx, error_process_len
    int 0x80
    mov eax, -1
    pop edx
    pop ecx
    pop ebx
    ret

; Memory allocation (स्मृति)
; Parameters:
;   eax = size in bytes
; Returns:
;   eax = pointer to allocated memory (or null on error)
स्मृति:
    push ebx
    push ecx
    
    mov ebx, eax        ; size
    mov eax, 45         ; sys_brk
    xor ecx, ecx
    int 0x80
    
    test eax, eax
    js memory_error
    
    pop ecx
    pop ebx
    ret

memory_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_memory
    mov edx, error_memory_len
    int 0x80
    xor eax, eax        ; Return null
    pop ecx
    pop ebx
    ret

; Get system information (तन्त्रांश)
; Parameters: none
; Returns:
;   eax = system uptime in seconds
;   ebx = total memory
;   ecx = free memory
तन्त्रांश:
    push edx
    
    ; Get uptime
    mov eax, 13         ; sys_time
    int 0x80
    push eax
    
    ; Get memory info (simplified)
    mov eax, 116        ; sys_sysinfo
    int 0x80
    
    pop eax             ; Restore uptime
    pop edx
    ret

; Read file (पठन)
; Parameters:
;   esi = file path
;   edi = buffer
;   edx = buffer size
; Returns:
;   eax = bytes read (or -1 on error)
पठन:
    push ebx
    push ecx
    
    ; Open file
    mov eax, 5          ; sys_open
    mov ebx, esi        ; path
    mov ecx, 0          ; O_RDONLY
    int 0x80
    
    test eax, eax
    js file_error
    
    ; Read file
    push eax            ; Save file descriptor
    mov ebx, eax        ; file descriptor
    mov eax, 3          ; sys_read
    mov ecx, edi        ; buffer
    int 0x80
    
    push eax            ; Save bytes read
    
    ; Close file
    pop ebx             ; Restore file descriptor
    mov eax, 6          ; sys_close
    int 0x80
    
    pop eax             ; Restore bytes read
    pop ecx
    pop ebx
    ret

; Write file (लेखन)
; Parameters:
;   esi = file path
;   edi = buffer
;   edx = buffer size
; Returns:
;   eax = bytes written (or -1 on error)
लेखन:
    push ebx
    push ecx
    
    ; Open file
    mov eax, 5          ; sys_open
    mov ebx, esi        ; path
    mov ecx, 65         ; O_WRONLY | O_CREAT
    mov edx, 0644o      ; mode
    int 0x80
    
    test eax, eax
    js file_error
    
    ; Write file
    push eax            ; Save file descriptor
    mov ebx, eax        ; file descriptor
    mov eax, 4          ; sys_write
    mov ecx, edi        ; buffer
    int 0x80
    
    push eax            ; Save bytes written
    
    ; Close file
    pop ebx             ; Restore file descriptor
    mov eax, 6          ; sys_close
    int 0x80
    
    pop eax             ; Restore bytes written
    pop ecx
    pop ebx
    ret

file_error:
    mov eax, 4
    mov ebx, 2          ; stderr
    mov ecx, error_file
    mov edx, error_file_len
    int 0x80
    mov eax, -1
    pop ecx
    pop ebx
    ret