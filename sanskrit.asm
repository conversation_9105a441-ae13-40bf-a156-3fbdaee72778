section .data
    ; Register allocation tracking
    reg_allocation_table times 8 dq 0  ; Track register usage (rax through rdi)
    reg_spill_area resq 4           ; Temporary storage for register spills

    ; Enhanced tokenizer configuration 
    token_buffer_size equ 1024
    max_token_length equ 64
    
    ; Token types
    TOKEN_KEYWORD equ 1
    TOKEN_IDENTIFIER equ 2
    TOKEN_NUMBER equ 3
    TOKEN_STRING equ 4
    TOKEN_OPERATOR equ 5
    TOKEN_ERROR equ 6
    TOKEN_EOF equ 7

section .bss
    ; Tokenizer buffers
    token_buffer resb token_buffer_size
    current_token resb max_token_length
    token_type resb 1
    token_length resq 1
    
    ; Symbol table
    symbol_table_size equ 256
    symbol_entry_size equ 32
    symbol_table resb symbol_table_size * symbol_entry_size
    symbol_hash_table resb symbol_table_size * 8
    symbol_count resq 1

section .text
    global _start

_start:
    ; Initialize the interpreter
    call init_interpreter
    
    ; Exit program
    mov rax, 60     ; sys_exit
    xor rdi, rdi    ; status = 0
    syscall

init_interpreter:
    push rbp
    mov rbp, rsp
    ; Initialize data structures here
    mov rsp, rbp
    pop rbp
    ret

skip_whitespace:
    push rbp
    mov rbp, rsp
    
.loop:
    movzx rax, byte [rsi]
    cmp al, 0x20    ; Space
    je .skip
    cmp al, 0x09    ; Tab
    je .skip
    cmp al, 0x0A    ; Line feed
    je .skip
    cmp al, 0x0D    ; Carriage return
    je .skip
    jmp .done
    
.skip:
    inc rsi
    jmp .loop
    
.done:
    mov rsp, rbp
    pop rbp
    ret

parse_ascii_token:
    push rbp
    mov rbp, rsp
    
    ; Check for operators
    movzx rax, byte [rsi]
    cmp al, '+'
    je .operator
    cmp al, '-'
    je .operator
    cmp al, '*'
    je .operator
    cmp al, '/'
    je .operator
    
    ; Check for numbers
    cmp al, '0'
    jb .not_number
    cmp al, '9'
    ja .not_number
    mov byte [token_type], TOKEN_NUMBER
    jmp .done
    
.operator:
    mov byte [token_type], TOKEN_OPERATOR
    jmp .done
    
.not_number:
    mov byte [token_type], TOKEN_IDENTIFIER
    
.done:
    mov rsp, rbp
    pop rbp
    ret

check_print:
check_var:
check_if:
check_loop:
check_read:
check_array:
check_comment:
    ret

find_symbol:
    ret

process_identifier:
    ret

var_assign:
    ret