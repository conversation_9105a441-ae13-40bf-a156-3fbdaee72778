/**
 * Transliteration Support for Sanskrit Programming Language
 * लिप्यंतरण समर्थन (Transliteration Support)
 * 
 * Supports multiple transliteration schemes:
 * - IAST (International Alphabet of Sanskrit Transliteration)
 * - Harvard-Kyoto
 * - ITRANS
 * - Roman to Devanagari
 */

class SanskritTransliterator {
  constructor() {
    // IAST to Devanagari mapping
    this.iastToDevanagari = {
      // Vowels
      'a': 'अ', 'ā': 'आ', 'i': 'इ', 'ī': 'ई', 'u': 'उ', 'ū': 'ऊ',
      'ṛ': 'ऋ', 'ṝ': 'ॠ', 'ḷ': 'ऌ', 'ḹ': 'ॡ', 'e': 'ए', 'ai': 'ऐ',
      'o': 'ओ', 'au': 'औ',
      
      // Consonants
      'k': 'क', 'kh': 'ख', 'g': 'ग', 'gh': 'घ', 'ṅ': 'ङ',
      'c': 'च', 'ch': 'छ', 'j': 'ज', 'jh': 'झ', 'ñ': 'ञ',
      'ṭ': 'ट', 'ṭh': 'ठ', 'ḍ': 'ड', 'ḍh': 'ढ', 'ṇ': 'ण',
      't': 'त', 'th': 'थ', 'd': 'द', 'dh': 'ध', 'n': 'न',
      'p': 'प', 'ph': 'फ', 'b': 'ब', 'bh': 'भ', 'm': 'म',
      'y': 'य', 'r': 'र', 'l': 'ल', 'v': 'व',
      'ś': 'श', 'ṣ': 'ष', 's': 'स', 'h': 'ह',
      
      // Special characters
      'ṃ': 'ं', 'ḥ': 'ः', '~': '्'
    };

    // Harvard-Kyoto to Devanagari mapping
    this.harvardKyotoToDevanagari = {
      // Vowels
      'a': 'अ', 'A': 'आ', 'i': 'इ', 'I': 'ई', 'u': 'उ', 'U': 'ऊ',
      'R': 'ऋ', 'RR': 'ॠ', 'lR': 'ऌ', 'lRR': 'ॡ', 'e': 'ए', 'ai': 'ऐ',
      'o': 'ओ', 'au': 'औ',
      
      // Consonants
      'k': 'क', 'kh': 'ख', 'g': 'ग', 'gh': 'घ', 'G': 'ङ',
      'c': 'च', 'ch': 'छ', 'j': 'ज', 'jh': 'झ', 'J': 'ञ',
      'T': 'ट', 'Th': 'ठ', 'D': 'ड', 'Dh': 'ढ', 'N': 'ण',
      't': 'त', 'th': 'थ', 'd': 'द', 'dh': 'ध', 'n': 'न',
      'p': 'प', 'ph': 'फ', 'b': 'ब', 'bh': 'भ', 'm': 'म',
      'y': 'य', 'r': 'र', 'l': 'ल', 'v': 'व',
      'z': 'श', 'S': 'ष', 's': 'स', 'h': 'ह',
      
      // Special characters
      'M': 'ं', 'H': 'ः'
    };

    // ITRANS to Devanagari mapping
    this.itransToDevanagari = {
      // Vowels
      'a': 'अ', 'aa': 'आ', 'A': 'आ', 'i': 'इ', 'ii': 'ई', 'I': 'ई',
      'u': 'उ', 'uu': 'ऊ', 'U': 'ऊ', 'RRi': 'ऋ', 'RRI': 'ॠ',
      'LLi': 'ऌ', 'LLI': 'ॡ', 'e': 'ए', 'ai': 'ऐ', 'o': 'ओ', 'au': 'औ',
      
      // Consonants
      'k': 'क', 'kh': 'ख', 'g': 'ग', 'gh': 'घ', 'N^': 'ङ',
      'ch': 'च', 'Ch': 'छ', 'chh': 'छ', 'j': 'ज', 'jh': 'झ', 'JN': 'ञ',
      'T': 'ट', 'Th': 'ठ', 'D': 'ड', 'Dh': 'ढ', 'N': 'ण',
      't': 'त', 'th': 'थ', 'd': 'द', 'dh': 'ध', 'n': 'न',
      'p': 'प', 'ph': 'फ', 'b': 'ब', 'bh': 'भ', 'm': 'म',
      'y': 'य', 'r': 'र', 'l': 'ल', 'v': 'व', 'w': 'व',
      'sh': 'श', 'Sh': 'ष', 's': 'स', 'h': 'ह',
      
      // Special characters
      'M': 'ं', 'H': 'ः', '.n': 'ं', '.h': 'ः'
    };

    // Programming keywords mapping
    this.programmingKeywords = {
      // English to Sanskrit
      'function': 'कार्य',
      'variable': 'परिवर्तनीय',
      'if': 'यदि',
      'else': 'अन्यथा',
      'while': 'पुनरावृत्ति',
      'for': 'पुनरावर्तनम्',
      'return': 'प्रत्यावर्तनम्',
      'print': 'लेखय',
      'read': 'पठनम्',
      'class': 'वर्ग',
      'import': 'आयातय',
      'try': 'प्रयत्नः',
      'catch': 'ग्रहणम्',
      'throw': 'फेंकना',
      'switch': 'चयन',
      'match': 'मिलान',
      'true': 'सत्य',
      'false': 'मिथ्या',
      'null': 'शून्य',
      'number': 'संख्या',
      'string': 'पङ्क्तिः',
      'list': 'सूची',
      'boolean': 'बूलियनः',
      
      // Roman transliterations to Sanskrit
      'karya': 'कार्य',
      'parivartaniya': 'परिवर्तनीय',
      'yadi': 'यदि',
      'anyatha': 'अन्यथा',
      'punaravritti': 'पुनरावृत्ति',
      'punaravartanam': 'पुनरावर्तनम्',
      'pratyavartanam': 'प्रत्यावर्तनम्',
      'lekhay': 'लेखय',
      'pathanam': 'पठनम्',
      'varg': 'वर्ग',
      'ayatay': 'आयातय',
      'prayatnah': 'प्रयत्नः',
      'grahanam': 'ग्रहणम्',
      'phenkana': 'फेंकना',
      'chayan': 'चयन',
      'milan': 'मिलान',
      'satya': 'सत्य',
      'mithya': 'मिथ्या',
      'shunya': 'शून्य',
      'sankhya': 'संख्या',
      'panktih': 'पङ्क्तिः',
      'suchi': 'सूची',
      'booleanah': 'बूलियनः'
    };

    // Devanagari numbers
    this.devanagariNumbers = {
      '0': '०', '1': '१', '2': '२', '3': '३', '4': '४',
      '5': '५', '6': '६', '7': '७', '8': '८', '9': '९'
    };

    // Reverse mappings for Devanagari to Roman
    this.devanagariToRoman = {};
    Object.keys(this.programmingKeywords).forEach(roman => {
      const devanagari = this.programmingKeywords[roman];
      this.devanagariToRoman[devanagari] = roman;
    });
  }

  /**
   * Convert IAST transliteration to Devanagari
   */
  iastToDevanagari(text) {
    let result = text;
    
    // Sort by length (longest first) to handle multi-character mappings
    const sortedKeys = Object.keys(this.iastToDevanagari).sort((a, b) => b.length - a.length);
    
    sortedKeys.forEach(key => {
      const regex = new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      result = result.replace(regex, this.iastToDevanagari[key]);
    });
    
    return result;
  }

  /**
   * Convert Harvard-Kyoto transliteration to Devanagari
   */
  harvardKyotoToDevanagari(text) {
    let result = text;
    
    const sortedKeys = Object.keys(this.harvardKyotoToDevanagari).sort((a, b) => b.length - a.length);
    
    sortedKeys.forEach(key => {
      const regex = new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      result = result.replace(regex, this.harvardKyotoToDevanagari[key]);
    });
    
    return result;
  }

  /**
   * Convert ITRANS transliteration to Devanagari
   */
  itransToDevanagari(text) {
    let result = text;
    
    const sortedKeys = Object.keys(this.itransToDevanagari).sort((a, b) => b.length - a.length);
    
    sortedKeys.forEach(key => {
      const regex = new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      result = result.replace(regex, this.itransToDevanagari[key]);
    });
    
    return result;
  }

  /**
   * Convert programming keywords from English/Roman to Sanskrit
   */
  translateProgrammingKeywords(text) {
    let result = text;
    
    // Sort by length (longest first) to handle multi-word keywords
    const sortedKeys = Object.keys(this.programmingKeywords).sort((a, b) => b.length - a.length);
    
    sortedKeys.forEach(key => {
      const regex = new RegExp(`\\b${key}\\b`, 'gi');
      result = result.replace(regex, this.programmingKeywords[key]);
    });
    
    return result;
  }

  /**
   * Convert Arabic numerals to Devanagari numerals
   */
  convertNumbers(text) {
    let result = text;
    
    Object.keys(this.devanagariNumbers).forEach(arabic => {
      const regex = new RegExp(arabic, 'g');
      result = result.replace(regex, this.devanagariNumbers[arabic]);
    });
    
    return result;
  }

  /**
   * Convert Devanagari back to Roman (for editing/debugging)
   */
  devanagariToRoman(text) {
    let result = text;
    
    Object.keys(this.devanagariToRoman).forEach(devanagari => {
      const regex = new RegExp(devanagari, 'g');
      result = result.replace(regex, this.devanagariToRoman[devanagari]);
    });
    
    // Convert Devanagari numbers back to Arabic
    Object.keys(this.devanagariNumbers).forEach(arabic => {
      const devanagari = this.devanagariNumbers[arabic];
      const regex = new RegExp(devanagari, 'g');
      result = result.replace(regex, arabic);
    });
    
    return result;
  }

  /**
   * Auto-detect transliteration scheme and convert to Devanagari
   */
  autoTransliterate(text) {
    // First try programming keywords
    let result = this.translateProgrammingKeywords(text);
    
    // Then try different transliteration schemes
    // Check for IAST markers (diacritics)
    if (/[āīūṛṝḷḹṅñṭḍṇśṣṃḥ]/.test(text)) {
      result = this.iastToDevanagari(result);
    }
    // Check for Harvard-Kyoto markers (capitals)
    else if (/[AIURGTDJNSHM]/.test(text)) {
      result = this.harvardKyotoToDevanagari(result);
    }
    // Check for ITRANS markers
    else if (/(?:aa|ii|uu|RRi|LLi|ch|Ch|Th|Dh|sh|Sh)/.test(text)) {
      result = this.itransToDevanagari(result);
    }
    
    // Convert numbers
    result = this.convertNumbers(result);
    
    return result;
  }

  /**
   * Smart transliteration for code input
   * Handles mixed content (code + comments)
   */
  smartTransliterate(code) {
    const lines = code.split('\n');
    const transliteratedLines = lines.map(line => {
      // Check if line is a comment
      if (line.trim().startsWith('//') || line.trim().startsWith('टिप्पणी')) {
        return line; // Don't transliterate comments
      }
      
      // Check if line contains string literals
      const stringRegex = /"([^"]*)"/g;
      let result = line;
      let match;
      const strings = [];
      
      // Extract strings
      while ((match = stringRegex.exec(line)) !== null) {
        strings.push(match[1]);
      }
      
      // Transliterate non-string parts
      result = result.replace(stringRegex, '###STRING###');
      result = this.autoTransliterate(result);
      
      // Restore strings
      strings.forEach(str => {
        result = result.replace('###STRING###', `"${str}"`);
      });
      
      return result;
    });
    
    return transliteratedLines.join('\n');
  }

  /**
   * Get available transliteration schemes
   */
  getAvailableSchemes() {
    return [
      { id: 'auto', name: 'Auto-detect', description: 'Automatically detect and convert' },
      { id: 'iast', name: 'IAST', description: 'International Alphabet of Sanskrit Transliteration' },
      { id: 'harvard-kyoto', name: 'Harvard-Kyoto', description: 'Harvard-Kyoto transliteration' },
      { id: 'itrans', name: 'ITRANS', description: 'Indian Language Transliteration' },
      { id: 'keywords', name: 'Keywords Only', description: 'Convert programming keywords only' },
      { id: 'devanagari', name: 'Devanagari', description: 'Direct Devanagari input' }
    ];
  }

  /**
   * Transliterate using specific scheme
   */
  transliterate(text, scheme = 'auto') {
    switch (scheme) {
      case 'iast':
        return this.iastToDevanagari(text);
      case 'harvard-kyoto':
        return this.harvardKyotoToDevanagari(text);
      case 'itrans':
        return this.itransToDevanagari(text);
      case 'keywords':
        return this.translateProgrammingKeywords(text);
      case 'devanagari':
        return text; // No conversion needed
      case 'auto':
      default:
        return this.smartTransliterate(text);
    }
  }
}

// Export for use in different environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SanskritTransliterator;
} else if (typeof window !== 'undefined') {
  window.SanskritTransliterator = SanskritTransliterator;
}
