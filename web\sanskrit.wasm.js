// WebAssembly module for Sanskrit interpreter

let sanskritModule;
let sanskritMemory;

// Initialize the WebAssembly module
async function initSanskritInterpreter() {
    try {
        const response = await fetch('sanskrit.wasm');
        const wasmBuffer = await response.arrayBuffer();
        const wasmModule = await WebAssembly.instantiate(wasmBuffer, {
            env: {
                memory: new WebAssembly.Memory({ initial: 256 }), // 16MB initial memory
                // System call implementations
                sys_write: (fd, ptr, len) => {
                    if (fd === 1 || fd === 2) { // stdout or stderr
                        const bytes = new Uint8Array(sanskritMemory.buffer, ptr, len);
                        const text = new TextDecoder().decode(bytes);
                        const output = document.getElementById('output');
                        output.innerHTML += `<span>${text}</span>`;
                        output.scrollTop = output.scrollHeight;
                    }
                    return len;
                },
                sys_read: (fd, ptr, len) => {
                    if (fd === 0) { // stdin
                        // Implement input handling if needed
                        return 0;
                    }
                    return -1;
                }
            }
        });

        sanskritModule = wasmModule.instance;
        sanskritMemory = sanskritModule.exports.memory;
        console.log('Sanskrit interpreter initialized');
        return true;
    } catch (error) {
        console.error('Failed to initialize Sanskrit interpreter:', error);
        return false;
    }
}

// Function to write code to WebAssembly memory
function writeCodeToMemory(code) {
    const encoder = new TextEncoder();
    const codeBuffer = encoder.encode(code);
    const ptr = sanskritModule.exports.allocate(codeBuffer.length + 1);
    const memory = new Uint8Array(sanskritMemory.buffer);
    memory.set(codeBuffer, ptr);
    memory[ptr + codeBuffer.length] = 0; // Null terminator
    return ptr;
}

// Execute Sanskrit code
async function executeSanskritCode(code) {
    if (!sanskritModule) {
        const initialized = await initSanskritInterpreter();
        if (!initialized) {
            throw new Error('Failed to initialize Sanskrit interpreter');
        }
    }

    try {
        const codePtr = writeCodeToMemory(code);
        const result = sanskritModule.exports.execute(codePtr);
        sanskritModule.exports.deallocate(codePtr);
        return result;
    } catch (error) {
        throw new Error(`Execution error: ${error.message}`);
    }
}

// Export the execute function
window.executeSanskritCode = executeSanskritCode;