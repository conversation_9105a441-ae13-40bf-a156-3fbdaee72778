; GUI Demo - Simple Window with Button and Textbox

section .data
    window_title db 'संस्कृत G<PERSON> प्रदर्शन', 0
    button_text db 'क्लिक करें', 0
    extern window_init
    extern create_window
    extern create_button
    extern create_textbox
    extern process_events

section .text
    global _start

_start:
    ; Initialize GUI system
    call window_init

    ; Create main window
    push window_title
    call create_window

    ; Create button at (50, 50)
    push 50     ; x
    push 50     ; y
    push button_text
    call create_button

    ; Create textbox at (50, 100)
    push 50     ; x
    push 100    ; y
    call create_textbox

    ; Process events
    call process_events

    ; Exit program
    mov eax, 1
    xor ebx, ebx
    int 0x80